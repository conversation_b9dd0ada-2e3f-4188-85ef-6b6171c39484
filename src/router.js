import DashboardMonitor from './views/dashboard-mon';
import DashboardMonitorOld from './views/dashboard-monitor';
import CreateTicket from './views/create-ticket';
import MainRemoteMonitor from './views/main-remote-monitor';
import ManageStats from './views/manage-stats';
import CustomerManager from './views/customer-manager';
import ViewTickets from './views/view-tickets';
import CreateTicketOriginal from './views/create-ticket-original';
import OrderDashboard from './views/order-dashboard';
import Diagnostics from './views/diagnostics-view';
import UserManager from './views/user-manager';
import DynamicDashboard from './views/dynamic-dashboard';
import auth2 from "./auth2";
import { createRouter, createWebHashHistory } from "vue-router";
import notify from 'devextreme/ui/notify';

import Home from "./views/home-page";
import Profile from "./views/profile-page";
import defaultLayout from "./layouts/side-nav-outer-toolbar";
import noAuthLayout from "./layouts/no-auth";
import simpleLayout from "./layouts/single-card";
import comingSoon from "./views/coming-soon";
import loginPage from "./views/login-page";

function loadView(view) {
  return () => import (/* webpackChunkName: "login" */ `./views/${view}.vue`)
}

const router = new createRouter({
  routes: [
    {
      path: "/home",
      name: "home",
      meta: {
        title: 'Home',
        requiresAuth: true,
        layout: defaultLayout
      },
      component: Home
    },
    {
      path: "/profile",
      name: "profile",
      meta: {
        title: 'Profile',
        requiresAuth: true,
        layout: defaultLayout
      },
      component: Profile
    },
    // {
    //   path: "/login-form",
    //   name: "login-form",
    //   meta: {
    //     requiresAuth: false,
    //     layout: simpleLayout,
    //     title: "Sign In"
    //   },
    //   component: loadView("login-form")
    // },
    {
    path: "/login",
    name: "login",
    meta: {
      requiresAuth: false,
      layout: noAuthLayout,
      title: "Sign In"
    },
    component: loginPage
  },

    // {
    //   path: "/reset-password",
    //   name: "reset-password",
    //   meta: {
    //     requiresAuth: false,
    //     layout: simpleLayout,
    //     title: "Reset Password",
    //     description: "Please enter the email address that you used to register, and we will send you a link to reset your password via Email."
    //   },
    //   component: loadView("reset-password-form")
    // },
    // {
    //   path: "/create-account",
    //   name: "create-account",
    //   meta: {
    //     requiresAuth: false,
    //     layout: simpleLayout,
    //     title: "Sign Up"
    //   },
    //   component: loadView("create-account-form"),
    // },
    // {
    //   path: "/change-password/:recoveryCode",
    //   name: "change-password",
    //   meta: {
    //     requiresAuth: false,
    //     layout: simpleLayout,
    //     title: "Change Password"
    //   },
    //   component: loadView("change-password-form")
    // },
    {
      path: "/",
      redirect: "/home"
    },
    {
      path: "/recovery",
      redirect: "/home"
    },
    {
      path: "/:pathMatch(.*)*",
      redirect: "/home"
    },
    {
      path: "/user-manager",
      name: "user-manager",
      meta: {
        requiresAuth: true,
        layout: defaultLayout
      },
      component: UserManager
    },
    {
      path: "/create-ticket-original",
      name: "create-ticket-original",
      meta: {
        requiresAuth: true,
        layout: defaultLayout
      },
      component: CreateTicketOriginal
    },
    {
      path: "/view-tickets",
      name: "view-tickets",
      meta: {
        title: 'View Tickets',
        requiresAuth: true,
        layout: defaultLayout
      },
      component: ViewTickets
    },
    {
      path: "/customer-manager",
      name: "customer-manager",
      meta: {
        title: 'Customer Manager',
        requiresAuth: true,
        layout: defaultLayout
      },
      component: CustomerManager
    },
    {
      path: "/manage-stats",
      name: "manage-stats",
      meta: {
        title: 'Connection/Stat Manager',
        requiresAuth: true,
        layout: defaultLayout
      },
      component: ManageStats
    },
    {
      path: "/main-remote-monitor",
      name: "main-remote-monitor",
      meta: {
        title: 'Remote Monitor',
        requiresAuth: true,
        layout: defaultLayout
      },
      component: MainRemoteMonitor
    },
    {
      path: "/create-ticket",
      name: "create-ticket",
      meta: {
        title: 'Create Ticket',
        requiresAuth: true,
        layout: defaultLayout
      },
      component: CreateTicket
    },
    {
      path: "/dashboard-monitor",
      name: "dashboard-monitor",
      meta: {
        title: 'Remote Monitor',
        requiresAuth: true,
        layout: defaultLayout
      },
      component: DashboardMonitor
    },
    {
      path: "/dashboard-monitor-old",
      name: "dashboard-monitor-old",
      meta: {
        title: 'Remote Monitor',
        requiresAuth: true,
        layout: defaultLayout
      },
      component: DashboardMonitorOld
    },
    {
      path: "/dashboard",
      name: "dynamic-dashboard",
      meta: {
        requiresAuth: true,
        // userSecurity: ['administrator', 'programmer'],
        layout: defaultLayout,
        title: 'Dashboard'
      },
      component: DynamicDashboard
    },
    {
      path: "/diagnostics-view",
      name: "diagnostics",
      meta: {
        requiresAuth: true,
        // userSecurity: ['administrator', 'programmer'],
        layout: defaultLayout,
        title: 'Logs & Diagnostics'
      },
      component: Diagnostics
    },
    {
      path: "/coming-soon",
      name: "coming-soon",
      meta: {
        requiresAuth: true,
        layout: defaultLayout,
        title: 'Stat Manager'
      },
      component: comingSoon
    },

  ],
  history: createWebHashHistory()
});

router.beforeEach((to, from, next) => {
  console.log('🛡️ Router guard - navigating to:', to.path);
  console.log('🛡️ Router guard - auth2.loggedIn():', auth2.loggedIn());

  if (to.matched.some(record => record.meta.requiresAuth)) {
    console.log('🛡️ Route requires auth');
    if (!auth2.loggedIn()) {
      console.log('🛡️ User not logged in, redirecting to login');
      next({
        name: "login",
        query: { redirect: to.fullPath }
      });
    } else {
      // Check userSecurity if defined in route meta
      if (to.meta.userSecurity) {
        console.log('🛡️ Route requires specific security level:', to.meta.userSecurity);
        auth2.getUser().then(user => {
          const securityLevel = user.data.security_level?.toUpperCase();
          console.log('🛡️ User security level:', securityLevel);
          
          if (securityLevel && to.meta.userSecurity.includes(securityLevel.toLowerCase())) {
            console.log('🛡️ User has required security level, allowing access');
            next();
          } else {
            console.log('🛡️ User lacks required security level, redirecting to home');
            notify('You do not have permission to access this page', 'warning', 5000);
            next({ name: 'home' });
          }
        });
      } else {
        console.log('🛡️ No specific security level required');
        next();
      }
    }
  } else {
    console.log('🛡️ Route does not require auth');
    next();
  }
});

export default router;
