<template>
	<DxLoadPanel :visible="isLoading"  :show-pane="true" :container="'.tab-panel'" :position="{ of: '.tab-panel' }" />
  <div class="content-block dx-card responsive-paddings">
    <div class="row">
			<div>
	
				<span>Location</span>
				<DxSelectBox :dataSource='customerSites.data' v-model='selectedSiteID' value-expr='idcustomers_sites' display-expr='site_name' styling-mode='contained'  placeholder="Select Location"
				:drop-down-options="{ width: '250px' }">
					
					<template #item="{ data }">
						<div class="text-color" >
							<strong>{{ data.customer_name }}</strong><br />
							<small>{{ data.site_name }}</small><br />
							<small>{{ data.formatted_address }}</small>
						</div>
					</template>
				</DxSelectBox>
			</div>
			<!-- <div>
				<span>Datetime</span>
				<DxDateBox v-model:value="selectedDate" type="date" styling-mode="underlined" />
			</div> -->
			<div>
				<span>Max Records</span>
				<DxNumberBox :width="80" :step="5" :max="1000" :min="30" :show-spin-buttons="isMobile? false: true" v-model:value="selectedMaxRecords" />
			</div>
			<div>
				<DxButton :width="100" text="Refresh" type="default" styling-mode="contained" @click="onRefreshClick" />
			</div>
		</div>
		<div class="tab-panel">
			<DxTabPanel  :style="`height: ${isMobile? 'calc(100vh - 265px)': 'calc(100vh - 287px)'};`" :swipe-enabled="false">
				<!-- Connections Tab -->
				<DxItem title="Connections" v-if="deviceMonitorData && deviceMonitorData.data && deviceMonitorData.data.WarehouseConnectionData">
					<div class="container">

						<DxDataGrid
							:height="'100%'"
							:data-source="connectionsData"
							:row-alternation-enabled="true"
							:column-hiding-enabled="true"
							:show-borders="false"
							:word-wrap-enabled="true"
							:column-auto-width="true"
							:no-data-text="gridMsgError"
							:allow-column-resizing="true"
							column-resizing-mode="nextColumn"
							allowColumnReordering="true" 
							@exporting="onExporting">
							
							<DxHeaderFilter :visible="true"/>
							<DxFilterPanel :visible="true"/>
							<DxScrolling mode="virtual" />
							<DxColumnFixing :enabled="true" />
							<DxColumnChooser :enabled="true" mode="dragAndDrop" />

							<DxColumn data-field="connection_name" caption="Device"></DxColumn>
							<DxColumn data-field="warehouse_connection_type" caption="Connection Type"></DxColumn>
							<DxColumn data-field="warehouse_connection_protocol_note" caption="Protocol"></DxColumn>
							<DxColumn data-field="warehouse_connection_general_note" caption="General Note"></DxColumn>
							<DxColumn data-field="warehouse_connection_heartbeat_in_range" caption="Connected" cell-template="connectedTemplate"></DxColumn>

							<template #connectedTemplate="{ data }">
								<div>
									<div class="status-indicator" :class="[ data.data.warehouse_connection_alive? 'status-all-active' : 'status-all-inactive' ] "></div>
								</div>
							</template>
						</DxDataGrid>
					</div>
				</DxItem>
				<template v-if="availableCommands && Array.isArray(availableCommands) && availableCommands.length > 0">
					<DxItem v-for="command in (availableCommands || [])" :key="command.remote_cmd_name" :title="command.remote_cmd_display_name">
						<div class="container">
							<template v-if="command && hasCommandVariables(command)">
								<div class="row grid-action-wrapper">
									<div v-for="variable in command.remote_cmd_vars" :key="variable.field_replace_name" class="command-variable-item">
										<!-- <span>{{ variable.field_display_name || variable.field_replace_name }}</span> -->
										<DxTextBox :placeholder="variable.field_display_name || variable.field_replace_name"  v-model="commandVariables[command.remote_cmd_name][variable.field_replace_name]"  styling-mode="contained"   width="200px"/>
									</div>
									<DxButton 
										text="Search" 
										type="default" 
										styling-mode="contained" 
										@click="() => executeRemoteSQLCommand(command)" 
										class="execute-command-btn"
									/>
								</div>
							</template>

							
							<fieldset v-if="commandResults[command.remote_cmd_name].length === 1" class="card">
								<legend>Info</legend>
								<fieldset v-for="(value, key) in commandResults[command.remote_cmd_name][0]" :key="key" style="padding: 10px;">
									<legend>{{key}}</legend>
									<label class="accent" :for="key">{{value}}</label>
								</fieldset>
							</fieldset>
							
							<DxDataGrid v-else
								:height="command && hasCommandVariables(command) ? 'calc(100%)' : '100%'" 
								:data-source="commandResults && commandResults[command.remote_cmd_name] || []" 
								:row-alternation-enabled="true"
								:column-hiding-enabled="true"
								:show-borders="false"
								:word-wrap-enabled="true"
								:column-auto-width="true" 
								:no-data-text="gridMsgError"
								:allow-column-resizing="true"
								column-resizing-mode="nextColumn"
								allowColumnReordering="true" 
								@exporting="onExporting">

								<DxHeaderFilter :visible="true"/>
								<DxExport :enabled="!hasCommandVariables(command)" />
								<DxSearchPanel :visible="!hasCommandVariables(command)" :highlight-case-sensitive="true" :width="`${isMobile ? '100%' : '300'}`"/>
								<DxFilterPanel :visible="true"/>
								<DxScrolling mode="virtual" />
								<DxColumnFixing :enabled="true" />
								<DxColumnChooser :enabled="true" mode="dragAndDrop" />
								<!-- DataGrid components -->
								
								<DxColumn v-for="column in (command && command.datagrid_fields || [])" 
										:key="column.db_field_name" 
										:data-field="column.db_field_name" 
										:data-type="column.data_type"
										:format="getFormat(column)"
										:allow-filtering="column.data_type !== 'date'"
										:caption="column.disp_field_name"
										:cell-template="column.data_type === 'boolean' ? 'booleanTemplate' : ''"/>

										<!-- <template #clearedTemplate="{ data }">
											<div style="border-radius: 10px; color: #fff;padding: 2px;" :style="{backgroundColor: data.value === false ? 'rgb(245 68 55 / 80%)' : 'rgb(140 195 74 / 80%)'}">
												{{data.value? 'Cleared':'Not Cleared'}}
											</div>	
										</template> -->
										<template #booleanTemplate="{ data }">
											<div>
												<div class="status-indicator" :class="[data.value? 'status-all-active' : 'status-all-inactive' ] ">
													<!-- {{ data.value ? 'Y' : 'N' }} -->
												</div>
											</div>
										</template>
							</DxDataGrid>
						</div>
					</DxItem>
				</template>
			</DxTabPanel>
		</div>
  </div>
</template>

<script setup>
	/*=====================================================================
    IMPORTS
 	=====================================================================*/
  //EXAMPLE: 
	// Vue core
  import { ref, computed, onMounted, watch, nextTick } from 'vue';

	// UI components & utilities
	import notify from 'devextreme/ui/notify';
	import DxTabPanel, { DxItem } from "devextreme-vue/tab-panel";
	import { DxDataGrid, DxColumn, DxSearchPanel,DxScrolling, DxFilterPanel, DxExport, DxGroupPanel, DxHeaderFilter, DxColumnFixing , DxColumnChooser} from 'devextreme-vue/data-grid';
	import { exportDataGrid } from 'devextreme/excel_exporter';
	import { saveAs } from 'file-saver';
	import { Workbook } from 'exceljs';
	import { DxButton } from 'devextreme-vue/button';
	import { DxDateBox }from 'devextreme-vue/date-box';
	import { DxNumberBox } from 'devextreme-vue/number-box';
	import { DxSelectBox } from 'devextreme-vue';
	import { DxLoadPanel } from 'devextreme-vue/load-panel';
	import { DxTextBox } from 'devextreme-vue/text-box';

	// Composables
	import auth from '../auth2'
	import { useRouter } from 'vue-router';
	import { useRestAPI } from '@/composables/useRestAPI'; 
	import { useRemoteSQL } from '@/composables/useRemoteSQL'; 
	import { useScreenSize } from '@/composables/useScreenSize';

	// Child components
	import orderLog from '@/components/order-log.vue';

	/*=====================================================================
    COMPOSABLE DEFINITIONS (EXPOSED STATES AND FUNCTIONS)  
  =====================================================================*/
	const router = useRouter();
	const {
		connectionStatus,
		fetchCustomerSites,
		fetchConnectionsBySite,
		sendEstablishMessage,
		selectedSiteID,
		selectedMaxRecords,
		customerSites,
		deviceMonitorData,
		isLoading,
		lastAPIError,
		connect,
		// Add these new imports
		availableCommands,
		commandResults,
		commandVariables, 
		fetchAvailableCommands,
		hasCommandVariables,
		executeRemoteSQLCommand
	} = useRemoteSQL();
	
	const { isMobile } = useScreenSize()
	

	/*=====================================================================
    LIFECYCLE HOOKS 
  =====================================================================*/
	//EXAMPLE: 
	onMounted(async () => {
		const user = await auth.getUser()
		if (!user.data.auth_token) { router.push('/login'); return }

		connect();
		
		// First fetch customer sites
		// await fetchCustomerSites();
	
		// if (selectedSiteID.value) {
		// 	fetchData();
		// }
	});


	/*=====================================================================
    ELEMENT REFS
  =====================================================================*/

	/*=====================================================================
    REFS FOR LOCAL STATE
  =====================================================================*/
	const notificationOffset = ref(0);
	const gridMsgError = ref('No Data Found');
	// Notification configuration object
	const notifyOptions = ref(
		{ 
			position: { my: "bottom", at: "bottom", of: ".content-block", offset: { x: 0, y: 10 }}, width: 'auto',
			animation: { show: { type: "fade", duration: 800, from: 0, to: 1 }, hide: { type: "fade", duration: 800, from: 1, to: 0 } }
		}
	)

	/*=====================================================================
    COMPUTED PROPERTIES 
  =====================================================================*/
	const connectionsData = computed(() => {
		return (
			deviceMonitorData.value?.data?.WarehouseConnectionData?.filter(
				(conn) => conn.warehouse_connection_type !== 'SERVICE'
			) || []
		)
	})

	const locations = computed(() => {

		return customerSites.value?.data || [];
	})


	/*=====================================================================
		FUNCTIONS
	=====================================================================*/
	
	const onRefreshClick = () => {
		fetchData();
	};

	const fetchData = async () => {
		try {
			// First check if we have customer sites data
			if (!customerSites.value || !customerSites.value.data || customerSites.value.data.length === 0) {
				console.log('No customer sites data found, fetching customer sites');
				await fetchCustomerSites();
			}
			
			// Only fetch connections if we have a selected site
			if (selectedSiteID.value) {
				const connections = await fetchConnectionsBySite();
				if(!connections || !connections.data || connections.data.length === 0){
					deviceMonitorData.value = { data: { WarehouseConnectionData: [] } };
					console.log('No connections found');
				}

				// Fetch available commands
				const commands = await fetchAvailableCommands();
				if (!commands || !commands.data || commands.data.length === 0) {
					console.log('No available commands found');
					availableCommands.value = [];
				}

				// Execute commands that don't require variables
				if (availableCommands.value && Array.isArray(availableCommands.value)) {
					availableCommands.value.forEach(command => {
						if (command && !hasCommandVariables(command)) {
							executeRemoteSQLCommand(command);
						}
					});
				}
			} else {
				console.log('No site selected yet, waiting for site selection');
			}
		} catch (error) {
			console.error('Error fetching data:', error);
			notify({...notifyOptions.value,  message: error.message || 'Unknown error' , type: 'error', displayTime: 5000 });
			
		}
	};

	const onExporting = ( e) => {
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Employees');

    exportDataGrid({
        component: e.component,
        worksheet: worksheet,
        autoFilterEnabled: true,
    }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer) => {
            saveAs(new Blob([buffer], { type: 'application/octet-stream' }), 'DataGrid.xlsx');
        });
    });

    e.cancel = true;
	};

	const showNotification = (message, type, time) => {
		notify({
			...notifyOptions.value, 
			position: { ...notifyOptions.value.position, offset: notificationOffset.value },  
			message, 
			type, 
			displayTime: time 
		});

		notificationOffset.value -= 40; 
		setTimeout(() => {
			notificationOffset.value += 40;
		}, time); // match displayTime
	};

	const getFormat = (field) =>{
		if (!field.field_format) return undefined;
		
		if (field.data_type === 'date'){
			return {
				type: 'custom',
				formatter: (value) => {
					if (!value) return '';
					return formatDate(new Date(value), field.field_format);
				}
			};
		}

		return undefined;
	};

	const formatDate = (date, formatStr) => {
		const pad = (n, width = 2) => String(n).padStart(width, '0');

		const map = {
			yyyy: date.getFullYear(),
			MM: pad(date.getMonth() + 1),
			dd: pad(date.getDate()),
			HH: pad(date.getHours()),
			hh: pad((date.getHours() % 12) || 12),
			mm: pad(date.getMinutes()),
			ss: pad(date.getSeconds()),
			tt: date.getHours() >= 12 ? 'PM' : 'AM'
		};
		console.log('formatDate:', date, formatStr)
		return formatStr.replace(/yyyy|MM|dd|HH|hh|mm|ss|tt/g, match => map[match]);
	};

	/*=====================================================================
    WATCHERS 
  =====================================================================*/
	watch(selectedSiteID, () => {
		// fetchData();
	});

	// Monitor connection status changes and initialize(establish) and send commands
	watch(connectionStatus, async (newStatus, oldStatus) => {
	
		if (newStatus === 'CONNECTED' && oldStatus !== 'CONNECTED') {
			// Initialize(establish) before sending commands
			sendEstablishMessage('REMOTE-SQL');

			// // Fetch customer sites await this because we need to select a site before fetching rest of data
			const sites = await fetchCustomerSites();
			// console.log('Sites:', sites);	
			// const tempID = sites.data.find(site => site.idcustomers_sites === 10)?.idcustomers_sites
			// selectedSiteID.value = tempID;
			fetchData();
		}
	});

	watch(lastAPIError, async(newError, oldError) => {
		if (newError) {
			console.error('API Error:', newError);

			// new Toast(document.body, { ...notifyOptions.value, newError, type: 'error', displayTime: 5000 }).show();
			showNotification(newError, 'error', 5000);
			// notify({...notifyOptions.value,  message: newError, type: 'error', displayTime: 5000 });
		
		}
	});	

</script>

<style scoped lang="scss">
	@import "../themes/generated/variables.additional.scss";

	.content-block {
		display: flex;
		flex-direction: column;
		flex: 1 1 auto;
		min-height: 0;
		overflow: hidden;
		position: relative;
		transition: all 0.3s ease;
		color: rgba($base-text-color, 0.65);
		gap: 20px;
	}

	.container {
    display: flex;
    flex-direction: column;
    // gap: 20px;
    flex: 1 1 auto;
    // overflow-y: auto;
    letter-spacing: 0.3px;
    border-radius: 8px;
    // border: 1px solid;
    // padding: 10px;
    // border-color: $base-border-color;
    height: 100%;
    // margin: 20px 0;
		color: rgba($base-text-color, 0.75);
  }
	.accent {
		color: $base-accent;
	}
	.text-color {
		color: rgba($base-text-color, 0.75);
	}
	
	.tab-panel {
		height: 100%;
	}
	fieldset {
		border: 2px dotted $base-border-color;
		border-radius: 10px;
		padding: 20px;
	}

	.card {
		display: flex;
    gap: 20px;
    flex-wrap: wrap;
		background-color: rgba($base-bg-dark, .65);
	}
	.field {
		padding: 20px;
    background-color: darkolivegreen;
    border-radius: 30px;
	}
	
	.row {
		display: flex;
		gap: 20px;
		flex-shrink: 0; 
		align-items: end;
		color: rgba($base-text-color, 0.75)!important;

	}

	.grid-action-wrapper{
		padding: 15px;
		border-bottom: 1px solid $base-border-color;
	}


	.status-indicator {
		width: 15px;
		height: 15px;
		border-radius: 50%;
		position: relative;
		transition: all 0.3s ease;
		justify-self: center;
		
	}

	.status-all-active {
		background: conic-gradient( from 0deg, #4CAF50 0deg 360deg );
		outline: 1px solid #4CAF50;;
	}

	.status-partial-active {
		background: conic-gradient( from 0deg, transparent 0deg 180deg, #ffd700 180deg 360deg );
		outline: 1px dotted #ffd700;
		
	}

	.status-all-inactive {
		background: #FF5252;
		outline: 1px solid #FF5252;;
	}


	// DX Text Editor Overrides
	::v-deep(.dx-texteditor) {
		border: 1px solid $base-border-color;
		border-radius: 5px;
		background-color: rgba($base-bg-dark, .65);
		padding: 0 10px;
		
	}
	
	::v-deep(.dx-texteditor-input) {
		color: rgba($base-text-color, 0.75)!important;
	}		
	::v-deep(.dx-texteditor::before) {
		content: none;
	}
	::v-deep(.dx-texteditor::after) {
		content: none;
	}
	

	// DX Button Overrides
	.custom-button.dx-button, .dx-button.dx-button-default , .dx-item-content.dx-button.dx-button-default {
		border-radius: 5px;
		box-shadow: none;
		height: 32px;
		margin-bottom: 1px;
		color: $base-text-color!important; //rgba($base-text-color, alpha($base-text-color)  * 0.85);
	}

	.custom-button {
		border-radius: 5px;
		box-shadow: none;
		height: 32px;
		margin-bottom: 1px;
		color: $base-text-color!important; //rgba($base-text-color, alpha($base-text-color)  * 0.85);
	}

	.custom-button.dx-button-has-icon ,.dx-button-content {
		min-width: unset!important;
		height: unset!important;
	}

	::v-deep(.dx-button-has-icon ){
		width: unset!important;
		height: unset!important;
	}

	::v-deep(.custom-button.dx-button-has-icon .dx-icon) {
		font-size: 10px !important;
		width: unset;
		height: unset;
	}

	// DX Data Grid Overrides
	::v-deep(.dx-datagrid-header-panel) {
		padding: 10px 20px!important;
		margin-bottom: 5px;;
	}
	::v-deep(.dx-datagrid ){
		color: rgba($base-text-color, 0.75)!important;
	}
	::v-deep(.dx-datagrid .dx-data-row td) {
		// word-break: break-word;
		// white-space: normal;
	}

	::v-deep(.dx-data-row .dx-command-adaptive span) {
		background-color: $base-accent;
    border-radius: 50%;
    padding: 3px;
		color: white;
		
	}

	::v-deep(.dx-header-row){
		background-color: rgba($base-bg-dark, .65);
	}

	::v-deep(.dx-empty-message) {
		margin-top: 40px;
		font-size: 16px;
		color: rgba($base-text-color, 0.75)!important;

	}
	
	// DX Tab Panel Overrides
	::v-deep(.dx-tab) {
		// border: 1px solid $base-border-color;
		border-bottom: 0;
		padding: 8px 25px;
		border-radius: 8px 8px 0px 0px;

	}
	::v-deep(.dx-tabs) {
		width: 200px;
		border-radius: 8px 8px 0px 0px;

	}
	::v-deep(.dx-tab-text) {
		font-weight: 400;
	}

	::v-deep(.dx-tab.dx-tab-selected) {
		background-color: rgba($base-bg-dark, .65);
		// border: 1px solid $base-border-color;
		border-bottom: 0;

		border-radius: 8px 8px 0px 0px;
	}
	::v-deep(.dx-tab.dx-state-focused) {
		background-color: rgba($base-bg-dark, .65)!important;
	}
	::v-deep(.dx-tab.dx-state-hover) {
		background-color: rgba($base-bg-dark, .65)!important;
	}
	::v-deep(.dx-tab.dx-state-active) {
		background-color: rgba($base-bg-dark, .65)!important;
	}

	.dx-tabpanel {

		display: block;
		overflow: auto;
		border: 1px solid #515159;
		border-radius: 8px;
	}
	::v-deep(.dx-tabpanel-tabs) {

		overflow-x: auto;
		height: auto;
		display: block;
	}
	::v-deep(.dx-tabs-wrapper ){
		position: relative;
		display: flex;
		// border: 1px solid $base-border-color;
		// border-right: 2px solid $base-border-color;
		border-bottom: 0;
		border-radius: 8px 8px 0px 0px;
		// overflow: scroll;
	
	}
	::v-deep(.dx-tabpanel-container ) {
		height: 100%!important;
	}
	::v-deep(.dx-multiview-wrapper) {
		padding: 0px;
		background-color: #2f2e38;
    border-top: 1px solid #515159;



	}
	
	::v-deep(.dx-numberbox-spin-container){
		width:10px
	}
	
	::v-deep(.dx-texteditor .dx-texteditor-buttons-container:last-child>.dx-numberbox-spin-container:last-child){
		margin-inline-end: unset;
	}

</style>