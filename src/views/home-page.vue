<template>

    <!-- <h2 class="content-block">Home</h2> -->

  <div class="content-block dx-card responsive-paddings">

      <!-- <h2 style="text-align: center;">Welcome To SIM Software - 24/7 Service / Monitoring</h2> -->
      <div style="text-align: center;">
        <h2 style="margin: 0; font-weight: 400;">Welcome To <span class="accent"> SIM CLOUD</span></h2>
        <h5 style="margin: 0;font-weight: 300;">A Systems in Motion automation platform.</h5>
        <div class="accent">24/7 Service / Monitoring</div>
      </div>
      <div style="text-align: center;">	<img src="@/assets/SIM_LOGO_NO_BACKGROUND.svg" class="logo"/></div>
      <div id="kvk-container">

        <a style="font-size: large;" href="https://www.systemsinmotioninc.com/" target="_blank">SIM Software</a> - Alsip, IL, USA
    
        <p id="kvk-container">Version {{ websiteVersion }}</p>
      </div>

  </div>


</template>

<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import databaseName from '../myFunctions/databaseName';
import notify from 'devextreme/ui/notify';
import auth from '@/auth2';

const router = useRouter();

databaseName.isSomeoneLoggedIn().then(user => {
  console.log(user);
  if (user.data.sim_first_name == null) {
    let sessionStorageUser = sessionStorage.getItem("KOZ_SERVICE_AUTH");
    let sessionStorageJSON = JSON.parse(sessionStorageUser);

    if(sessionStorageJSON != null)
    {
      auth._simUser = sessionStorageJSON.data;
      if(databaseName.validateAuthLevel('any'))
      {
        //put primer stuff in here for like functions to call to get data
        //'any' value passed to function will allow auth for anyuser
        console.log("I Got Validated");
      }
      else
      {
        notify('Your Account Does NOT Have Access To This Page', 'warning', 10000);
        router.push('/home');
      }
    }
    else
    {
      notify('Please Log In To Use The Service Site', 'warning', 10000);
      router.push('/login');
    }
  }
});

const isMobile = ref(false);
const websiteVersion = ref("loading...");

websiteVersion.value = databaseName.getWebsiteVersion();
// databaseInfo.value = databaseName.getPaths();
databaseName.checkWebsiteVersion();
isMobile.value = databaseName.getScreenSizeSmall();

</script>

<style scoped lang="scss">
@import "../themes/generated/variables.additional.scss";
.content-block {
    display: flex;
    flex-direction: column;
    flex: auto;
    min-height: 0;
    align-content: center;
    overflow: hidden;
    position: relative;
    color: rgba($base-text-color, alpha($base-text-color)  * 0.65);
    letter-spacing: 0.3px!important;
    gap: 20px;
  }

  .accent {
		color: $base-accent;
	}

	.bg-accent {
		background-color: $base-accent;
	}
#kvk-container{
  text-align: center;
}
#company-image{
  width: 20%;
  margin-bottom: 0px;
  padding-top: 3%;
  padding-bottom: 3%;
}
#hiddenDiv{
  position: relative;
  margin: auto;
  width: 100%;
}
.home-device-btn{
  position: relative;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: 20px;
}
.home-grid{
  position: relative;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: 20px;
}
.home-refresh{
  left: 0;
  right: 0;
  margin: auto;
  margin-bottom: 20px;
  float: left;
  margin-right: 20px;
}
.hide-btn{
  width: 100%;
}
#home-hidden-title{
  width: 320px;
  padding-bottom: 50px;
  margin: auto;
  right: 0;
  left: 0;
}
.home-time{
  float: left;
}
.disabled-home{
  background-color: rgb(197, 197, 197);
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 10px;
}
.on-home{
  background-color: #90EE90;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 10px;
}
.off-home{
  background-color: #FF0000;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 10px;
}
#hiddenDiv{
  position: relative;
  margin: auto;
  width: 100%;
}
.groupContainer{
  display: flex;
  align-items: center;
}
// .homeGreenAlarm {
//   height: 15px;
//   width: 15px;
//   background-color: #90EE90;
//   margin-right: 5px;
// }
// .homeRedAlarm {
//   height: 15px;
//   width: 15px;
//   background-color: #FF0000;
//   margin-right: 5px;
// }
	.logo {
		width: 150px;
		height: 150px;
		background-color: $base-bg-dark;
		border-radius: 50%;
		padding: 15px;
		box-shadow: 2.87px 2.87px 12px 0px rgba(65, 191, 179, 0.25);
	}
</style>