<template>
  <div class="content-block dx-card responsive-paddings" ref="wrap">
    
    <div class="slide-container">
      <!-- Modified popup to act as slide-out panel -->
      <!-- :width="'calc(100% - 375px)'" -->
      <dx-popup
        :visible="isSlideOutVisible"
        :hide-on-outside-click="false"
        :show-title="true"
        :show-close-button="true"
        :drag-enabled="false"
        :container="isMobile? '.dx-drawer-content': popupContainer"
        :position="isMobile ? mobilePopupPosition : popupPosition"
        :width="'50%'"
        height="100%"
       
        :min-width="isMobile? '100%': '715'"
        :shading="false"
        :animation="popupAnimation"
        @hiding="onHiding"
        title="Connections"
      >
     
        <template #default>
          <div class="slide-panel-content">
            <!-- <div>
              <dx-button @click="toggleSlideOut" text="Toggle Slide Panel" />
            </div> -->
            <!-- content -->
            <div v-if="selectedLocation" >
              <div class="accent" :style="`font-size: ${isMobile? 'medium': 'x-large'};`">{{ selectedLocation.customer_name }} - {{ selectedLocation.site_name  }}</div>
              <div>{{ selectedLocation.formatted_address }}</div>
            </div>
          
            
            <WarehouseConnections v-if="selectedLocation?.koz_tracks_remote_stats" 
              :connections="siteConnections.WarehouseConnectionData"
              :scannerStats="siteConnections.ScannerStats"
              :scannerColumns="siteConnections.ScannerColumns"
              :loading="isDataLoading"
              :isMobile="isMobile"
              initialExpandedType="">
            </WarehouseConnections>

            <div v-else class="sales-banner">
              This location does not support live tracking. <br>
              Please contact sales for more information. <br> <br>
              <a href="<EMAIL>">Contact Sales</a>
            </div>

          </div>
        </template>
      </dx-popup>
    </div>

    <div class="row">
      
      <div v-if="!isMobile" class="map-wrapper">
        <div class="row" style="justify-content: center; margin-bottom: 20px; margin-top:10px">
          <div class="list-item" style="padding: 3px 8px;">Locations: <span class="accent">{{ locations.length }}</span></div>
          <div class="list-item" style="padding: 3px 8px;">Tracked: <span class="accent">{{ locations.filter(obj => obj.koz_tracks_remote_stats === true).length }}</span></div>
          <div class="list-item" style="padding: 3px 8px;">Issues: <span class="accent">{{ locations.filter(obj => obj.koz_reported_problem === true).length }}</span></div>
        </div>

        <!-- :bounds="mapBounds" -->
        <DxVectorMap 
          :tooltip="mapTooltipConfig"
          :onClick="handleMarkerClick"
          @tooltip-shown="handleTooltipShown"
          :panningEnabled="true"
          :zoom-factor='10'
          :center="[-95, 38]">
          
          <DxLayer :data-source="usa" :hover-enabled="false"></DxLayer>
          <DxLayer
            name="locations"
            type="marker"
            :data-source="mapData"
            :hover-enabled="true"
            :customize="customizeMarkers"
            element-type="bubble"
            :selected-mode="'single'"
            :selection="{ color: '#dc3545' }">
          </DxLayer>
          
          <!-- <DxTooltip :enabled="true" :customize-tooltip="customizeTooltip" ></DxTooltip> -->

          <DxControlBar :enabled="false"></DxControlBar>


          <DxTooltip :enabled="true" content-template="tooltipTemplate" cornerRadius="10" color="#2B2A33"> </DxTooltip>
          <template #tooltipTemplate="{ data }">
            <!-- {{ data.layer.index }}
            {{ data.layer.hasOwnProperty('elementType') }}
            {{ data }} -->
            <div v-if="data.layer.index === 1">
              <div class="map-tooltip">
                <div class="tooltip-title">{{mapData[data.index].attributes.name}}</div>
                <div class="tooltip-subtitle">{{mapData[data.index].attributes.site_name}}</div>
                <div class="tooltip-address">{{mapData[data.index].attributes.address}}</div>
                <div class="row">
                  <div v-if="mapData[data.index].attributes.trackingStatus" class="tooltip-status tracking">Tracking</div>
                  <div v-if="mapData[data.index].attributes.problemStatus" class="tooltip-status problem">Reported Issue</div>
                </div>
            </div>
          </div>
          </template>
        </DxVectorMap>
      </div>

      <div>
        <div v-if="isMobile" class="row" style="justify-content: center; margin-bottom: 20px;">
          <div class="list-item" style="padding: 3px 8px;">Locations: <span class="accent">{{ locations.length }}</span></div>
          <div class="list-item" style="padding: 3px 8px;">Tracked: <span class="accent">{{ locations.filter(obj => obj.koz_tracks_remote_stats === true).length }}</span></div>
          <div class="list-item " style="padding: 3px 8px;">Issues: <span class="accent">{{ locations.filter(obj => obj.koz_reported_problem === true).length }}</span></div>
        </div>
        <!-- Toggle switch -->
        <div class="filter-toggle-container">
          <span class="toggle-label">Show Tracked Locations Only:</span>
          <DxSwitch v-model:value="showOnlyRemoteStats" width="40" @value-changed="onToggleChange" />
          <!-- <span class="toggle-status">{{ showOnlyRemoteStats ? 'On' : 'Off' }}</span> -->
        </div>
  
        <DxList
          :data-source='filteredLocations'
          :show-selection-controls='false'
          :scrolling-enabled='true'
          :allow-item-deleting='false'
          selection-mode='single'
          :width="isMobile? '100%': '320'"
          height="calc(100vh - 275px)"
          :search-enabled="true"
          :search-mode="searchMode"
          search-expr="customer_name"
          @selection-changed='selectionChanged'
          @item-click="selectionChanged"
        >
          <template #item="{ data: item }">
            <div class="list-item">
              <div class="row">
                <!-- Conditional indicator based on remote stats and problem status -->
                <div 
                  class="stats-indicator" 
                  :class="{
                    'remote-stats-disabled': !item.koz_tracks_remote_stats,
                    'remote-stats-enabled': item.koz_tracks_remote_stats && !item.koz_reported_problem,
                    'remote-stats-problem': item.koz_tracks_remote_stats && item.koz_reported_problem
                  }"
                ></div>
                <div>{{item.customer_name}}</div>
                <div>({{item.site_name}})</div>
                <div style="flex:auto"></div>
              </div>
              <div style="padding-top: 5px; opacity: .5;">{{item.formatted_address}}</div>
            </div>
          </template> 
        </DxList>
      </div>
    
    </div>
  </div>
</template>

<script setup>
import { 
  DxVectorMap, 
  DxLayer, 
  DxControlBar, 
  DxTooltip,
  DxBorder
} from 'devextreme-vue/vector-map';
import * as mapsData from 'devextreme-dist/js/vectormap-data/usa.js';
import DxList from 'devextreme-vue/list';
import DxSwitch from 'devextreme-vue/switch';
import { DxPopup, DxButton } from 'devextreme-vue';
import { ref, computed , onMounted, watch} from 'vue';
import { useRestAPI } from '@/composables/useRestAPI'; 
import auth2 from '../auth2'
import { useRouter } from 'vue-router';
import WarehouseConnections from '@/components/warehouse-connections.vue';
import { useScreenSize } from '@/composables/useScreenSize';

const { isMobile } = useScreenSize()
// Initialize the REST API functionality
const api = useRestAPI()

const router = useRouter();

const wrap = ref(null)
const popupContainer = ref(null);

const isSlideOutVisible = ref(false)
const searchMode = ref('contains');
const usa = mapsData.usa
// Add toggle state
const showOnlyRemoteStats = ref(true);


// Custom animation configuration for sliding effect
const popupAnimation = {
  show: {
    type: 'slide',
    from: { left: '-100%', opacity: 1 }, // Start from outside the left edge
    to: { left: '0%', opacity: 1 }      // Slide to the left edge
  },
  hide: {
    type: 'slide',
    from: { left: '0%', opacity: 1 },    // Start from the left edge
    to: { left: '-100%', opacity: 1 }   // Slide out to the left
  }
};

// Location data
const locations = ref([ ]);

const siteConnections = ref({})
const isDataLoading = ref(false)
// UI state
const selectedLocation = ref(null);

// DEMO GARBAGE
const useAliases = ref(false)
const customerLookup = {}
const siteLookup = {}
const siteDefinitions = [
  { customer_name: 'KeHE', customer_name_alias: 'Customer Demo' },
  { customer_name: 'School Specialty', customer_name_alias: 'Customer Demo 2' },
  { customer_name: 'SIM SOFTWARE', customer_name_alias: 'Customer Demo 3' },
  { customer_name: 'Winston Brands', customer_name_alias: 'Customer Demo 4' },
  { site_name: 'site1', site_name_alias: 'Main Office' },
  { site_name: 'site2', site_name_alias: 'Branch Location' }
  // Add more mappings as needed
]

siteDefinitions.forEach(def => {
  if (def.customer_name) {
    customerLookup[def.customer_name] = def.customer_name_alias
  }
  if (def.site_name) {
    siteLookup[def.site_name] = def.site_name_alias
  }
})


onMounted(async() => {
  // get locations
  const user = await auth2.getUser()
  if (!user.data.auth_token) { router.push('/login'); return }

  let endpoint = '';
  if (user.data.user_type === 'sim') {
    endpoint = 'OverallScreen';
  }else {
    endpoint = `CustomerOverallScreen/${user.data.idcustomers}`;
  }
  const res = await api.get(`/RemoteStats/${endpoint}`)

  // const res = await api.get('RemoteStats/OverallScreen');

  if (useAliases.value) {
      // Map the data with the alias definitions
      locations.value = res.data.map(item => {
        const mappedItem = { ...item }

        // Replace with aliases if they exist
        if (item.customer_name in customerLookup) {
          mappedItem.customer_name = customerLookup[item.customer_name]
        }
        
        if (item.site_name in siteLookup) {
          mappedItem.site_name = siteLookup[item.site_name]
        }
        
        return mappedItem
      })
    } else {
      // Use original data without aliases
      locations.value = res.data
    }

  console.log('dashboard-mon:',res.data)
  // Set the container to the wrapper div once it's mounted
  popupContainer.value = wrap.value;
});

// Toggle change handler
const onToggleChange = (e) => {
  showOnlyRemoteStats.value = e.value;
};
const toggleSlideOut = (e) => {
  isSlideOutVisible.value = !isSlideOutVisible.value;
};

const onHiding =() => { 
  isSlideOutVisible.value = false
}

// Add filtered locations computed property
const filteredLocations = computed(() => {

  // First filter by toggle state
  let result = showOnlyRemoteStats.value ? locations.value.filter(location => location.koz_tracks_remote_stats === true) : locations.value;

  // Then sort alphabetically by customer_name and then by site_name
  return result.sort((a, b) => {
    // First compare by customer_name
    const customerComparison = a.customer_name.localeCompare(b.customer_name);
    
    // If customer names are the same, compare by site_name
    if (customerComparison === 0) {
      return a.site_name.localeCompare(b.site_name);
    }
    
    return customerComparison;
  });
});

// Filter locations with valid coordinates
const validLocations = computed(() => 
  filteredLocations.value.filter(location => 
    location.lat_coordinate && 
    location.lng_coordinate &&
    !isNaN(location.lat_coordinate) && 
    !isNaN(location.lng_coordinate)
  )
);

// Prepare map data - now uses filteredLocations
const mapData = computed(() => 
  validLocations.value.map(location => ({
    coordinates: [location.lng_coordinate, location.lat_coordinate], 
    attributes: {
      id: location.idcustomers_sites,
      name: `${location.customer_name} - ${location.site_name}`,
      address: location.formatted_address,
      trackingStatus: location.koz_tracks_remote_stats,
      problemStatus: location.koz_reported_problem,
      customerName: location.customer_name,
      siteName: location.site_name
    }
  }))
);

// Calculate map bounds to fit all markers
const mapBounds = computed(() => {
  if (validLocations.value.length === 0) {
    return [-125, 24, -67, 49]; // Default US bounds
  }
  
  const lngs = validLocations.value.map(l => l.lng_coordinate);
  const lats = validLocations.value.map(l => l.lat_coordinate);
  
  return [
    Math.min(...lngs) - 5,
    Math.min(...lats) - 5,
    Math.max(...lngs) + 5,
    Math.max(...lats) + 5
  ];
});

  // Custom position configuration to ensure proper alignment relative to container
  const popupPosition = computed(() => ({
    my: "left top",
    at: "left top",
    of: popupContainer.value,
    offset: { x: 0, y: 0 }
  }));

  const mobilePopupPosition = computed(() => ({
    my: "left top",
    at: "left top", 
    of: ".dx-drawer-content",
    offset: { x: 0, y: 0 }
  }));

	/*=====================================================================
    WATCHERS 
  =====================================================================*/
	// Monitor connection status changes
	watch(selectedLocation, (newLoc, oldLoc) => {
		if (newLoc !== oldLoc) {

			if (!isSlideOutVisible.value) {
        toggleSlideOut()
      }

      // get site connections
      getSiteConnections()
		} 
	});

  // Map tooltip configuration
  const mapTooltipConfig = { enabled: true };


	/*=====================================================================
    FUNCTIONS 
  =====================================================================*/
  const getSiteConnections = async () => {
    isDataLoading.value = true

    if (selectedLocation.value.koz_tracks_remote_stats){
      const res = await api.get(`RemoteStats/OverallScreen/${selectedLocation.value.idcustomers_sites}`);  
      siteConnections.value = res.data
    }else {
      siteConnections.value = []
    }
    
    isDataLoading.value = false
  }


const selectionChanged = (e) => {
  if (e.addedItems && e.addedItems.length > 0) {
    selectedLocation.value = e.addedItems[0];
  }
  isSlideOutVisible.value = isSlideOutVisible.value ? isSlideOutVisible.value: true
};
const customizeText = ({ index, start, end }) => {
  console.log(index, start, end)
  if (index === 0) {
    return '< 0.5%';
  }

  return (index === 5) ? '> 3%' : `${start}% to ${end}%`;
};
const customizeMarkers = (markers) => {
  markers.forEach((marker) => {
    const mk = mapData.value.find(item => item.attributes.id === marker.attribute('id'));
    const hasRemoteStats = !!mk.attributes.trackingStatus;
    const isProblem = !!mk.attributes.problemStatus;
    
    // First check if location has remote stats tracking
    let markerColor, hoverColor;
    
    if (hasRemoteStats) {
      // For locations with remote stats, check if there's a problem
      if (isProblem) {
        // Remote stats + problem
        markerColor = '#dc3545'; // Red
        hoverColor = '#c82333';
      } else {
        // Remote stats + no problem
        markerColor = '#28a745'; // Green
        hoverColor = '#218838';
      }
    } else {
      // No remote stats - use gray regardless of problem status
      markerColor = '#6c757d'; // Gray
      hoverColor = '#5a6268';
    }
    
    marker.applySettings({
      size: 14,
      color: markerColor,
      borderColor: '#ffffff',
      borderWidth: 1,
      hoverColor: hoverColor
    });
  });
};



// Event handlers
const handleMarkerClick = (e) => {
  try {
    if (e?.target?.layer?.type === 'marker' && e.target.attribute) {
      const markerData = e.target.attribute();
      if (markerData?.id) {
        selectedLocation.value = validLocations.value.find(loc => loc.idcustomers_sites === markerData.id);
        isSlideOutVisible.value = isSlideOutVisible.value ? isSlideOutVisible.value: true
      }
    }
  } catch (error) {
    console.error('Error in handleMarkerClick:', error);
  }
};


</script>

<style lang="scss" scoped>
  @import "../themes/generated/variables.additional.scss";

.content-block {
    color: rgba($base-text-color, 0.65)!important;
    display: flex;
    flex-direction: column;
    flex: 1 1 auto;
    min-height: 0;
    
    overflow: hidden;
    position: relative;
    transition: all 0.3s ease;

    ::v-deep(.dx-list-item) {
      color: rgba($base-text-color, 0.75)!important;
    }
    ::v-deep(.dx-state-hover) {
      background-color: $base-bg!important;
    }
    ::v-deep(.dx-list-item-selected) {
      background-color: $base-bg!important;
    }
    ::v-deep(.dx-state-focused) {
      background-color: $base-bg!important;
    }
    
  }

  .container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    flex: 1 1 auto;
    overflow-y: auto;
    letter-spacing: 0.3px;
    position: relative;
  }

  .row {
    display: flex;
    gap: 10px;
    flex-shrink: 0; 
  }

  /* color classes */
	.accent {
		color: $base-accent;
	}
	.bg-accent {
		background-color: $base-accent;
	}

  
  /* Toggle switch container styling */
  .filter-toggle-container {
    margin-bottom: 10px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  .toggle-label {
    font-weight: 500;
  }
  
  .toggle-status {
    font-weight: 500;
    min-width: 30px;
  }

  .map-wrapper {
    position: relative;
    min-width: 500px;
    flex: auto;
    border: unset;
    border-radius: 4px;
    overflow: hidden;
  }
  ::v-deep(.dx-visibility-change-handler) rect{

    stroke-width: 0;
  }
  .location-details {
    margin-top: 20px;
    padding: 15px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
  }

  .location-grid {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
  }

  .location-grid th, .location-grid td {
    border: 1px solid #dee2e6;
    padding: 8px 12px;
    text-align: left;
  }

  .location-grid th {

  }

  .status-tag {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 0.85em;
    font-weight: bold;
    margin-right: 5px;
  }

  .status-tag.tracking {
    background-color: #28a745;
    color: white;
  }

  .status-tag.problem {
    background-color: #dc3545;
    color: white;
  }

  /* DevExtreme tooltip styling */
  ::v-deep(.dxm-tooltip div) {
    background-color: rgb(151, 91, 183);
    border: 5px solid pink
  }

  .map-tooltip {
    padding: 5px;
    
  }
  

  .tooltip-title {
    font-weight: bold;
    font-size: 14px;
    margin-bottom: 5px;
  }

  .tooltip-subtitle {
    font-weight: bold;
    margin-bottom: 5px;
  }

  .tooltip-address {
    margin-bottom: 5px;
  }

  .tooltip-status {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
    margin-top: 5px;
  }

  .tooltip-status.tracking {
    background-color: #28a745;
    color: white;
  }

  .tooltip-status.problem {
    background-color: #dc3545;
    color: white;
  }

  .list-item {

    background-color: rgba($base-bg-dark, .65);
    border-bottom: 5px solid $base-bg;
    padding: 10px;
    border-radius: 10px;
  }

  .stats-indicator {
    width: 15px;
    height: 15px;
    min-width: 15px;
    border-radius: 50%;
    margin-right: 8px;
    transition: all 0.3s ease;
  }

  .remote-stats-disabled {
    background: #6c757d;  /* Gray for no remote stats */
    border: 2px solid #fff;

  }

  .remote-stats-enabled {
    background: #28a745;  /* Green for remote stats without problems */
    border: 2px solid #fff;
  }

  .remote-stats-problem {
    background: #dc3545;  /* Red for remote stats with problems */
    border: 2px solid #fff;
  }

  .sales-banner {
    padding:40px;
    margin: 40px;
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    border: 1px solid #43afa886;
    border-radius: 10px;
    box-shadow: rgb(55 170 162 / 30%) 0px 0px 8px 2px, rgba(55, 170, 162, 0.08) 0px 0px 0px 0px;
  }


  /* DX Controls Override  */
  ::v-deep(.dx-texteditor, .dx-texteditor-container,  .dx-editor-filled , .dx-texteditor.dx-texteditor-container){
    border: 0px;
    min-width: 120px;
    border: 1px solid $base-border-color;
    border-radius: 5px;
    background-color: rgba($base-bg-dark, 0.5);

  }

  
  .dx-texteditor{
    background-color: rgba($base-bg-dark, 0.5);
    opacity: 1;
    
  }
  ::v-deep(.dx-texteditor.dx-editor-filled::before ){
    content: none;
  }
  ::v-deep(.dx-texteditor.dx-editor-filled::after ){
    content: none;
  }
  


  .custom-button.dx-button-has-icon  {
    min-width: unset!important;
  }

  ::v-deep(.custom-button.dx-button-has-icon .dx-icon) {
    font-size: 8px !important;
    width: unset;
    height: unset;
  }


  ::v-deep(.dx-data-row .dx-command-adaptive span) {
		background-color: $base-accent;
    border-radius: 50%;
    padding: 3px;
    color: white;
		
	}


  /* Custom CSS to make the popup slide in from left */
  :deep(.dx-popup) {
    transition: transform 0.3s ease-out;
  }

  /* Add these classes for animation control */
  :deep(.dx-popup-wrapper .dx-overlay-content) {
    transform: translateX(-100%);
    box-shadow: 4px 0 8px rgba(0, 0, 0, 0.1);

  }

  :deep(.dx-popup-wrapper.dx-state-visible .dx-overlay-content) {
    transform: translateX(0);
  }

  .slide-panel-content {
    padding: 20px;
    color: rgba($base-text-color, 0.75)!important;
    .screen-x-small & {

				padding: 0px;
			}
  }
</style>