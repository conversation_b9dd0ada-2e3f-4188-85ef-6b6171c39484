<template>
  <div class="content-block dx-card responsive-paddings">
    <!-- Page content goes here -->
  </div>
</template>

<script setup>
	/*=====================================================================
    IMPORTS
 	=====================================================================*/
  //EXAMPLE: 
	// Vue core
  import { ref, computed, onMounted, watch, nextTick } from 'vue';

	// UI components & utilities
	import notify from 'devextreme/ui/notify';

	// Composables
	// import {useOrderStart} from '@/composables/useOrderStart' 

	// Child components
	// import ZoomableContainer from '../components/zoomable-container.vue'

	/*=====================================================================
    COMPOSABLE DEFINITIONS (EXPOSED STATES AND FUNCTIONS)  
  =====================================================================*/
	//EXAMPLE: 
	// const { orderStartStations, fetchOrderStartStations } = useOrderStart();
	

	/*=====================================================================
    LIFECYCLE HOOKS 
  =====================================================================*/
	//EXAMPLE: 
	onMounted(async () => {
		
	});


	/*=====================================================================
    ELEMENT REFS
  =====================================================================*/
	//EXAMPLE: 
	const zoomContainer = ref()


	/*=====================================================================
    REFS FOR LOCAL STATE
  =====================================================================*/
	// Notification configuration object
	//EXAMPLE: 
	const firstName = ref(String);
	const lastName = ref(String);
	const notifyOptions = ref(
		{ 
			position: { my: "bottom", at: "bottom", of: ".content-block", offset: { x: 0, y: 10 }}, width: 'auto',
			animation: { show: { type: "fade", duration: 800, from: 0, to: 1 }, hide: { type: "fade", duration: 800, from: 1, to: 0 } }
		}
	)



	/*=====================================================================
    COMPUTED PROPERTIES 
  =====================================================================*/
	//EXAMPLE: 
	const fullName = computed(() => {
		return firstName.value + '' + lastName.value
	})

	/*=====================================================================
		FUNCTIONS
	=====================================================================*/
	const showNotification = (result, successMessage) => {
		if (result.success) {
			notify({ ...notifyOptions.value, message: successMessage, type: 'success', displayTime: 2000 });
		} else {
			notify({ ...notifyOptions.value, message: result.error, type: 'error', displayTime: 3000 });
		}
	};

	/*=====================================================================
    WATCHERS 
  =====================================================================*/
	//EXAMPLE: 
	watch(firstName, (newData, oldData) => { 

})

</script>

<style scoped lang="scss">
	@import "../themes/generated/variables.additional.scss";

	.content-block {
		display: flex;
		flex-direction: column;
		flex: 1 1 auto;
		min-height: 0;
		overflow: hidden;
		position: relative;
		transition: all 0.3s ease;
		color: rgba($base-text-color, 0.65);
		gap: 40px;
	}
</style>