export default [
  {
    text: "Home",
    path: "/home",
    icon: "home"
  },
  // {
  //   text: 'Remote Monitor',
  //   path: '/dashboard-monitor',
  //   icon: 'eyeopen'
  // },
  {
    text: 'Remote Monitor',
    shortText: 'Monitor',
    path: '/dashboard-monitor',
    icon: 'eyeopen'
  },
  {
    text: 'Dashboard',
    path: '/dashboard',
    icon: 'mediumiconslayout',
  },
  // {
  //   text: 'Remote Monitor',
  //   path: '/main-remote-monitor',
  //   icon: 'eyeopen'
  // },
  {
    text: "Tickets",
    icon: "box",
    isExpanded: true,
    items:[
      // {
      //   text: 'Create Ticket Original',
      //   path: '/create-ticket-original'
      // },
      {
        text: 'Create Ticket',
        path: '/create-ticket',
      },
      {
        text: 'View Tickets',
        path: '/view-tickets'
      }
    ]
  },
  {
    text: 'Diagnostics',
    path: '/diagnostics-view',
    icon: 'preferences',
  },
  {
    text: "Admin",
    icon: "card",
    userTypeAccess: 'sim',
    items:[
      {
        text: 'Customer Manager',
        path: '/customer-manager'
      },
      {
        text: 'Connection/Stat Manager',
        path: '/manage-stats'
      }
    ]
  },

  
  
];
