@import "./themes/generated/variables.additional.scss";
.content {
  line-height: 1.5;
  flex-grow: 1;
  display: flex;

  h2 {
    font-size: 30px;
    margin-top: 20px;
    margin-bottom: 20px;
  }
}

.container {
  height: 100%;
  flex-direction: column;
  display: flex;
}

.layout-body {
  flex: 1;
  min-height: 0;
  
  .screen-x-small & {
    background-color: $base-bg;
  }
}
.header-toolbar{
  .screen-x-small & {
    // background-color: $base-bg-dark!important;
    border-bottom: 1px solid $base-border-color;
  }
}
.page-title{
  .screen-x-small & {
    margin:10px 20px 0px 20px ;
    padding: 0;
    font-size: 14px;
  }
}

.side-nav-outer-toolbar .dx-drawer {
  height: calc(100% - 70px)
}

.content-block {
  margin-left: 40px;
  margin-right: 40px;
  margin-bottom: 40px;

  .screen-x-small & {
    border-radius: 0;
    margin-left: 0px;
    margin-right: 0px;
    margin-bottom: 0px;
  }
}

.responsive-paddings {
  padding: 20px;

  .screen-large & {
    padding: 40px;
  }
}
/* Scrollbar styling */
::-webkit-scrollbar {
  width: 12px; 
}

::-webkit-scrollbar-track {
  background: transparent;
  border: 4px solid transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba($base-text-color, 0.3);
  border-radius: 8px;
  border: 4px solid transparent; /* Creates padding effect */
  background-clip: padding-box;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba($base-text-color, 0.3);
}

.dx-card.wide-card {
  border-radius: 0;
  margin-left: 0;
  margin-right: 0;
  border-right: 0;
  border-left: 0;
}
.dx-card {
  border-radius: 12px;

  .screen-x-small & {
    box-shadow: none;
  }
}

.with-footer > .dx-scrollable-wrapper >
.dx-scrollable-container > .dx-scrollable-content {
  height: 100%;

  & > .dx-scrollview-content {
    display: flex;
    flex-direction: column;
    min-height: 100%;
  }
}

#app {
  height: 100%;
 }

$side-panel-min-width: 60px;

@mixin glassmorphic-glow(
  $primary-color: hsl(191, 80%, 70%),
  $secondary-color: hsl(222, 80%, 70%),
  $border-radius: 15px,
  $bg-color: rgba(51, 50, 61, 0.3),
  $glow-opacity: 0.9,
  $bg-gradient-opacity: 0.1,
  $bg-gradient-spread: 33%
) {
  position: relative;
  border-radius: $border-radius;
  backdrop-filter: blur(12px);
  box-shadow: 
    rgba(0, 0, 0, 0.3) 0px 10px 16px -8px,
    rgba(0, 0, 0, 0.2) 0px 20px 36px -14px;
  
  // Controllable background gradients
  background: 
    linear-gradient(235deg, 
      #{rgba($primary-color, $bg-gradient-opacity)} 0%, 
      transparent #{$bg-gradient-spread}
    ), 
    linear-gradient(45deg, 
      #{rgba($secondary-color, $bg-gradient-opacity * 0.8)} 0%, 
      transparent #{$bg-gradient-spread}
    ), 
    linear-gradient(#{$bg-color});

  &::before {
    content: '';
    position: absolute;
    top: -1px;
    right: -1px;
    width: 75%;
    height: 75%;
    aspect-ratio: 1;
    border-radius: 0 #{$border-radius} 0 0;
    border: 1px solid transparent;
    background: conic-gradient(
      from -45deg at center,
      transparent 12%,
      #{rgba($primary-color, $glow-opacity)} 20%,
      transparent 35%
    ) border-box;
    mask: 
      linear-gradient(white, white) padding-box,
      linear-gradient(white, white);
    mask-composite: xor;
    -webkit-mask: 
      linear-gradient(white, white) padding-box,
      linear-gradient(white, white);
    -webkit-mask-composite: xor;
    pointer-events: none;
    z-index: 1;
  }

  &::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: -1px;
    width: 75%;
    height: 75%;
    aspect-ratio: 1;
    border-radius: 0 0 0 #{$border-radius};
    border: 1px solid transparent;
    background: conic-gradient(
      from 135deg at center,
      transparent 12%,
      #{rgba($secondary-color, $glow-opacity * 0.8)} 20%,
      transparent 35%
    ) border-box;
    mask: 
      linear-gradient(white, white) padding-box,
      linear-gradient(white, white);
    mask-composite: xor;
    -webkit-mask: 
      linear-gradient(white, white) padding-box,
      linear-gradient(white, white);
    -webkit-mask-composite: xor;
    pointer-events: none;
    z-index: 1;
  }
}

