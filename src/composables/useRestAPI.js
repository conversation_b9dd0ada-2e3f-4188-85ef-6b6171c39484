import { ref } from 'vue'
import auth2 from '../auth2';
import axios from 'axios'

/**
 * Composable for handling REST API communication
 * 
 * @param {Object} options - Configuration options
 * @param {string} options.baseURL - Base URL for API requests
 * @param {Object} options.headers - Default headers for requests
 * @param {number} options.timeout - Request timeout in milliseconds
 * @param {boolean} options.withCredentials - Whether to send cookies with requests
 * @returns {Object} API methods and state
 */
export function useRestAPI(options = {}) {

  const isLoading = ref(false)
  const error = ref(null)
  const data = ref(null)

  // Create an axios instance with provided options
	// Requests to '/api/...' are proxied based on the vue.config.js settings
  // I know we have the base URL in vue.config.js, but let's keep it flexible 
	// for other calls unrelated to our product and allow for environment variables as an option.
  const apiClient = axios.create({
    baseURL: options.baseURL || process.env.KOZ_API_URL || '/api',
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    },
    timeout: options.timeout || 30000,
    withCredentials: options.withCredentials || false
  })

  // Add request interceptor for auth token, logging, etc.
  apiClient.interceptors.request.use(
    async config => {
      // Only add Authorization if not already provided in the request config
      if (!config.headers.Authorization) {
       
        // Get auth token
        const user =  await auth2.getUser()
        const token = user?.data?.auth_token;
        
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }
      }
      return config
    },
    error => {
      console.error('Request error:', error)
      return Promise.reject(error)
    }
  )

  // Add response interceptor for error handling, response transformation
  apiClient.interceptors.response.use(
    response => {
      // Transform response data if needed
      return response
    },
    error => {
      // Handle specific error cases (like 401 Unauthorized)
      if (error.response && error.response.status === 401) {
        // Example: Redirect to login or refresh token
        console.warn('Unauthorized request - redirecting to login')
        // router.push('/login')
      }
      
      return Promise.reject(error)
    }
  )

  /**
   * Generic request method with automatic loading and error handling
   * 
   * @param {Function} requestFn - Function that returns a promise (axios request)
   * @returns {Promise} - Promise that resolves to the response data
   */
  const request = async (requestFn) => {
    isLoading.value = true
    error.value = null
    data.value = null

    try {
      const response = await requestFn()
      data.value = response.data
      return response.data
    } catch (err) {
      error.value = err.response?.data?.message || err.message || 'API request failed'
      console.error('API request error:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Perform a GET request
   * 
   * @param {string} url - URL to request
   * @param {Object} params - URL parameters
   * @param {Object} config - Additional axios config
   * @returns {Promise} - Promise that resolves to the response data
   */
  const get = (url, params = {}, config = {}) => {
    return request(() => apiClient.get(url, { ...config, params }))
  }

  /**
   * Perform a POST request
   * 
   * @param {string} url - URL to request
   * @param {Object} data - Data to send
   * @param {Object} config - Additional axios config
   * @returns {Promise} - Promise that resolves to the response data
   */
  const post = (url, data = {}, config = {}) => {
    return request(() => apiClient.post(url, data, config))
  }

  /**
   * Perform a PUT request
   * 
   * @param {string} url - URL to request
   * @param {Object} data - Data to send
   * @param {Object} config - Additional axios config
   * @returns {Promise} - Promise that resolves to the response data
   */
  const put = (url, data = {}, config = {}) => {
    return request(() => apiClient.put(url, data, config))
  }

  /**
   * Perform a PATCH request
   * 
   * @param {string} url - URL to request
   * @param {Object} data - Data to send
   * @param {Object} config - Additional axios config
   * @returns {Promise} - Promise that resolves to the response data
   */
  const patch = (url, data = {}, config = {}) => {
    return request(() => apiClient.patch(url, data, config))
  }

  /**
   * Perform a DELETE request
   * 
   * @param {string} url - URL to request
   * @param {Object} config - Additional axios config
   * @returns {Promise} - Promise that resolves to the response data
   */
  const del = (url, config = {}) => {
    return request(() => apiClient.delete(url, config))
  }

  /**
   * Create a resource client for a specific API resource
   * 
   * @param {string} resourcePath - Path to the resource (e.g., 'orders')
   * @returns {Object} - Object with methods for interacting with the resource
   */
  const createResourceClient = (resourcePath) => {
    const resourceUrl = `/${resourcePath}`
    
    return {
      getAll: (params = {}, config = {}) => get(resourceUrl, params, config),
      getById: (id, params = {}, config = {}) => get(`${resourceUrl}/${id}`, params, config),
      create: (data, config = {}) => post(resourceUrl, data, config),
      update: (id, data, config = {}) => put(`${resourceUrl}/${id}`, data, config),
      patch: (id, data, config = {}) => patch(`${resourceUrl}/${id}`, data, config),
      remove: (id, config = {}) => del(`${resourceUrl}/${id}`, config),
    }
  }

  /**
   * Creates a new request context with a custom token (used for admin override functionality- see sku-validate.vue)
   * 
   * @param {string} token - The token to use for requests in this context 
   * @returns {Object} - Object with the same methods but using the provided token
   */
  const withToken = (token) => {
    // Create config with the custom token
    const tokenConfig = {
      headers: {
        Authorization: token ? `Bearer ${token}` : undefined
      }
    }
    
    // Return an object with the same methods but using our custom token
    return {
      get: (url, params = {}, config = {}) =>  get(url, params, { ...config, ...tokenConfig }),
      post: (url, data = {}, config = {}) => post(url, data, { ...config, ...tokenConfig }),
      put: (url, data = {}, config = {}) => put(url, data, { ...config, ...tokenConfig }),
      patch: (url, data = {}, config = {}) => patch(url, data, { ...config, ...tokenConfig }),
      delete: (url, config = {}) => del(url, { ...config, ...tokenConfig }),
      
      // Also provide a way to create a resource client with this token
      createResourceClient: (resourcePath) => {
        const resourceUrl = `/${resourcePath}`
        
        return {
          getAll: (params = {}, config = {}) => get(resourceUrl, params, { ...config, ...tokenConfig }),
          getById: (id, params = {}, config = {}) => get(`${resourceUrl}/${id}`, params, { ...config, ...tokenConfig }),
          create: (data, config = {}) => post(resourceUrl, data, { ...config, ...tokenConfig }),
          update: (id, data, config = {}) => put(`${resourceUrl}/${id}`, data, { ...config, ...tokenConfig }),
          patch: (id, data, config = {}) => patch(`${resourceUrl}/${id}`, data, { ...config, ...tokenConfig }),
          remove: (id, config = {}) => del(`${resourceUrl}/${id}`, { ...config, ...tokenConfig }),
        }
      }
    }
  }

  return {
    // Core HTTP methods
    get,
    post,
    put,
    patch,
    delete: del, // 'delete' is a reserved word in JS
    
    // Resource client factory
    createResourceClient,

    // Token override helper
    withToken,
    
    // State
    isLoading,
    error,
    data,
    
    // Direct access to the axios instance
    apiClient
  }
}