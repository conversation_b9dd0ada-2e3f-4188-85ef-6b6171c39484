import { ref, onMounted, onUnmounted } from 'vue'

export function useScreenSize() {
  const windowWidth = ref(window.innerWidth)
  const isMobile = ref(windowWidth.value < 600)
  
  function updateWidth() {
    windowWidth.value = window.innerWidth
    isMobile.value = windowWidth.value < 600
  }
  
  onMounted(() => {
    window.addEventListener('resize', updateWidth)
  })
  
  onUnmounted(() => {
    window.removeEventListener('resize', updateWidth)
  })
  
  return {
    windowWidth,
    isMobile
  }
}