<template>

  <nav class="bottom-nav">
    
    <DxButton v-for="item in mainNavItems" :key="item.text" styling-mode="text" class="custom-button"
      @click="handleNav(item)">
      <template #default>
        <div class="button-with-text-bottom">
          <i :class="`dx-icon-${item.icon}`"></i>
          <span v-if="isMobile && item.shortText">{{item.shortText}}</span>
          <span v-else>{{item.text}}</span>
        </div>
      </template>
    </DxButton>

    <!-- More button if needed -->
    <DxButton v-if="moreNavItems.length" styling-mode="text" class="custom-button" 
      @click="openMoreMenu">
      <template #default>
        <div class="button-with-text-bottom">
          <i class="dx-icon-more"></i>
          <span>More</span>
        </div>
      </template>
    </DxButton>

    <!-- <button
      v-for="item in filteredNavItems" :key="item.text" @click="handleNav(item)" :class="{ active: isActive(item) }" >
      <i :class="`dx-icon-${item.icon}`"></i>
      
      <span>{{ item.text }}</span>
    </button> -->
    <!-- Slide-up sub-menu -->
    <transition name="slide-up">
      <div v-if="showSubMenu" class="bottom-sub-menu">
        
        <button v-for="sub in activeSubItems" :key="sub.text" @click="goToSub(sub)" >
           <i :class="`dx-icon-${sub.icon}`"></i>
          {{ sub.text }}
        </button>
       
      </div>
    </transition>
  </nav>
</template>

<script setup>
  import { ref, computed, onMounted } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import menuItems from '../app-navigation'
  import { getFilteredNavigation } from '../utils/navigation-filter';
  import DxButton from 'devextreme-vue/button';
  import { useScreenSize } from '@/composables/useScreenSize';

  const { isMobile } = useScreenSize()
  const router = useRouter()
  const route = useRoute()
  const filteredNavItems = ref([]);
  const subMenuStack = ref([])

  onMounted(async() => {
  filteredNavItems.value =  await getFilteredNavigation();

  });


  const mainNavItems = computed(() => filteredNavItems.value.slice(0, 4))
  const moreNavItems = computed(() => filteredNavItems.value.slice(4))

  // Only top-level items
  const navItems = computed(() =>
    menuItems.filter(item => !item.userTypeAccess || item.userTypeAccess === 'sim')
  )

  const showSubMenu = ref(false)
  const activeSubItems = ref([])
  const currentMenu = ref(null)

  function handleNav(item) {
    // If clicking the same menu, toggle the sub-menu
    if (showSubMenu.value && currentMenu.value === item) {
      showSubMenu.value = false
      currentMenu.value = null
      return
    }
    showSubMenu.value = false
    if (item.items && item.items.length) {
      activeSubItems.value = item.items
      currentMenu.value = item
      setTimeout(() => { showSubMenu.value = true }, 0)
    } else if (item.path) {
      router.push(item.path)
      currentMenu.value = null
    }
  }

  function goToSub(sub) {
    if (sub.items && sub.items.length) {
      subMenuStack.value.push(activeSubItems.value)
      activeSubItems.value = sub.items
    } else if (sub.path) {
      router.push(sub.path)
      showSubMenu.value = false
      subMenuStack.value = []
    }
  }

  function goBackSubMenu() {
    if (subMenuStack.value.length) {
      activeSubItems.value = subMenuStack.value.pop()
    }
  }

  function openMoreMenu() {
    // If already open, close it
    if (showSubMenu.value && currentMenu.value === 'more') {
      showSubMenu.value = false
      currentMenu.value = null
      return
    }
    showSubMenu.value = false
    activeSubItems.value = moreNavItems.value
    currentMenu.value = 'more'
    setTimeout(() => { showSubMenu.value = true }, 0)
  }

</script>

<style scoped lang="scss">
	@import "../themes/generated/variables.additional.scss";

  .bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 70px;
    color: rgba($base-text-color, 0.65);
    background-color: $base-bg-dark;
    display: flex;
    justify-content: space-around;
    align-items: center;
    z-index: 9999;
    padding-bottom: 5px;
  }
  .button-with-text-bottom {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 12px;
    text-overflow: ellipsis;
    white-space: nowrap;
    

    // icon
    i { font-size: 20px; }
  }
  .custom-button {
    
    width: 100%;
    height: 100%;
    border-radius: 0;
    color: rgba($base-text-color, 0.65);
  }
  .bottom-nav button {
    background: none;
    border: none;
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 12px;


  }
  .bottom-nav button.active {
    color: #3aafa9;
  }
  .nav-icon {
    font-size: 20px;
    margin-bottom: 2px;
  }
  .bottom-sub-menu {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 70px;           /* Sits just above the bottom nav */
    display: flex;
    flex-direction: column;
    background-color: $base-bg-dark;
    color: rgba($base-text-color, 0.65);
    i { font-size: 20px; }

  }
  .bottom-sub-menu button {
    padding: 16px;
    border: none;
    background: none;
    text-align: left;
    font-size: 12px;
    color: rgba($base-text-color, 0.65);
  }
</style>