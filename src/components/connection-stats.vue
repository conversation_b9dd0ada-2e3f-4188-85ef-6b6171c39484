<template>
	<DxLoadPanel :visible="isLoading"  :show-pane="true" :container="'.popup-content'" :position="{ of: '.popup-content' }" />
	<div class="popup-content" :style="{padding: isMobile? '0px': '20px'}">

		<div class="row">
			<div>
				<span>End Datetime</span>
				<DxDateBox v-model:value="endDateTime" type="datetime" styling-mode="underlined" 
				:width="isMobile? '165px': '100%'"
				:display-format="{
					type: 'custom',
					formatter: formatDateBoxDisplay
				}"/>
			</div>
			<div>
				<span>Past Hours</span>
				<DxNumberBox
					:step="3"
					:max="24"
					:min="1"
					:show-spin-buttons="isMobile? false: true"
					v-model:value="queryHours"
					styling-mode="underlined"
				/>
			</div>
			<div>
				<DxButton
					:width=" isMobile? '80px' : ''"
					text="Search"
					type="default"
					styling-mode="contained"
					@click="onRefreshClick"
				/>
			</div>
			
		</div>
		
		<DxTabPanel v-if="isLoading === false"
			:key="popupSelectionChanged"
			:data-source="gridData.stat_groups"
			:loop="true"
			:scroll-byContent="true"
			:scrolling-enabled="true"
			:animation-enabled="true"
			:swipe-enabled="false" 
			:no-data-text="gridMsgError"
			:height="`${isMobile? 'calc(100% - 150px)' : 'calc(100% - 80px)'}`">

			<template #title="{ data: group }">
				<span>{{ group.stat_group }}</span>
			</template>

			<template #item="{ data: group }">
				<div style="display: flex; flex-direction: column; gap: 20px;">
					<DxDataGrid 
						:height="'calc(50% - 10px)'" 
						:data-source="group.TimeRanges" 
						:row-alternation-enabled="true"
						:column-hiding-enabled="true"
						:show-borders="true"
						:word-wrap-enabled="true"
						:column-auto-width="true" 
						:no-data-text="gridMsgError">

						<DxColumn data-field="StartDatetime" caption="Start" :fixed="true" fixed-position="left" :width="isMobile? 'auto':'140'" cell-template="dateCellTemplate">
							
						</DxColumn>
						<DxColumn data-field="EndDatetime" caption="End" :fixed="true" fixed-position="left" :width="isMobile? 'auto':'140'" cell-template="dateCellTemplate"/>
						
						<DxColumn v-for="(val) in group.stat_columns" :key="val.stat_name" :data-field="val.stat_name" :caption="val.stat_name_disp" alignemnt="center" />
						
						<DxSummary>
							<DxTotalItem v-for="(val) in group.stat_columns" :key="val.stat_name" :column="val.stat_name" summary-type="sum" display-format="{0}" css-class="gridTotals" />
						</DxSummary>

						<template #dateCellTemplate="{ data }" >
							<span style="white-space: nowrap;">{{ formatShortDateTime(data.value) }} </span>
						</template>
					</DxDataGrid>

					<!-- Chart -->
					<DxChart :data-source="group.TimeRanges" title="Group Stats" :height="'calc(50% - 10px)'" >

						<DxTooltip :enabled="true" :content-template="graphToolTipFunctionRecentScans" :z-index="2000" />
						
						<DxCommonSeriesSettings argument-field="StartDatetime" type="line" hover-mode="allArgumentPoints" />
						
						<DxArgumentAxis argument-type="datetime">
							<DxLabel :staggering-spacing="10"  display-mode="stagger"/>
						</DxArgumentAxis>
						
						<DxSeries v-for="(val) in group.stat_columns" :key="val.stat_name" :value-field="val.stat_name" :name="val.stat_name_disp"/>
						
						<DxLegend vertical-alignment="bottom" horizontal-alignment="center" >
							<DxMargin :top="25"/>
						</DxLegend>

					</DxChart>
				</div>
			</template>
		</DxTabPanel>
           
	</div>
</template>

<script setup>
	/*=====================================================================
    IMPORTS
 	=====================================================================*/
	// Vue core
  import { ref, computed, onMounted, watch, defineProps, nextTick } from 'vue';
	import { DxTabPanel } from 'devextreme-vue/tab-panel';
	import { DxDataGrid, DxColumn, DxScrolling, DxFilterRow, DxSummary, DxTotalItem, } from 'devextreme-vue/data-grid';
	import DxChart, { DxSeries, DxCommonSeriesSettings,  DxTooltip,  DxLabel, DxMargin, DxLegend, DxArgumentAxis, } from 'devextreme-vue/chart';
	import { DxNumberBox } from 'devextreme-vue/number-box';
	import { DxDateBox }from 'devextreme-vue/date-box';
	import { DxSelectBox } from 'devextreme-vue/select-box';
	import { DxButton } from 'devextreme-vue/button';
	import { DxLoadPanel } from 'devextreme-vue/load-panel';

	// UI components & utilities
	// import notify from 'devextreme/ui/notify';

	// Composables
	import { useRestAPI } from '@/composables/useRestAPI'; 

	// Child components
	// import ZoomableContainer from '../components/zoomable-container.vue'

	/*=====================================================================
    COMPOSABLE DEFINITIONS (EXPOSED STATES AND FUNCTIONS)  
  =====================================================================*/
	const api = useRestAPI()

	/*=====================================================================
    PROPS
  =====================================================================*/
	const props = defineProps({
		siteID: { type: [Number, String, null], default: null },
		connectionID: { type: [Number, String, null], default: null },
		isMobile:  { type: Boolean, default: false },
	});
	

	/*=====================================================================
    LIFECYCLE HOOKS 
  =====================================================================*/
	//EXAMPLE: 
	onMounted(async () => {

	});


	/*=====================================================================
    ELEMENT REFS
  =====================================================================*/
	//EXAMPLE: 
	const zoomContainer = ref()
	

	/*=====================================================================
    REFS FOR LOCAL STATE
  =====================================================================*/
	// Notification configuration object
	//EXAMPLE: 
	const startDateTime = new Date()
	const endDateTime = ref(new Date())
	const timeInterval = ref(30);
	const queryHours = ref(4)
	const gridMsgError = ref('')
	const gridData = ref([])
	// const dummyData = ref({message:"Success Finding Stats Data In Selected Range",data:{connection_name:"SCAN01",stat_groups:[{stat_group:"DivConfirm",stat_columns:[{stat_name:"1",stat_name_disp:"Zone 01"},{stat_name:"2",stat_name_disp:"Zone 02"}],TimeRanges:[{stat_names:[{stat_name:"1",stat_count:73},{stat_name:"2",stat_count:59}],StartDatetime:"2025-04-04 11:30:00",EndDatetime:"2025-04-04 12:00:00"},{stat_names:[{stat_name:"1",stat_count:17},{stat_name:"2",stat_count:56}],StartDatetime:"2025-04-04 12:00:00",EndDatetime:"2025-04-04 12:30:00"},{stat_names:[{stat_name:"2",stat_count:76},{stat_name:"1",stat_count:34}],StartDatetime:"2025-04-04 12:30:00",EndDatetime:"2025-04-04 13:00:00"},{stat_names:[{stat_name:"2",stat_count:77},{stat_name:"1",stat_count:55}],StartDatetime:"2025-04-04 13:00:00",EndDatetime:"2025-04-04 13:30:00"},{stat_names:[{stat_name:"2",stat_count:11},{stat_name:"1",stat_count:45}],StartDatetime:"2025-04-04 13:30:00",EndDatetime:"2025-04-04 14:00:00"},{stat_names:[{stat_name:"1",stat_count:96},{stat_name:"2",stat_count:21}],StartDatetime:"2025-04-04 14:00:00",EndDatetime:"2025-04-04 14:30:00"},{stat_names:[{stat_name:"1",stat_count:15},{stat_name:"2",stat_count:24}],StartDatetime:"2025-04-04 14:30:00",EndDatetime:"2025-04-04 15:00:00"},{stat_names:[{stat_name:"2",stat_count:25},{stat_name:"1",stat_count:34}],StartDatetime:"2025-04-04 15:00:00",EndDatetime:"2025-04-04 15:30:00"},{stat_names:[{stat_name:"2",stat_count:10},{stat_name:"1",stat_count:13}],StartDatetime:"2025-04-04 15:30:00",EndDatetime:"2025-04-04 16:00:00"}]},{stat_group:"DivRequest",stat_columns:[{stat_name:"1",stat_name_disp:"Zone 01"},{stat_name:"2",stat_name_disp:"Zone 02"}],TimeRanges:[{stat_names:[{stat_name:"2",stat_count:59},{stat_name:"1",stat_count:67}],StartDatetime:"2025-04-04 11:30:00",EndDatetime:"2025-04-04 12:00:00"},{stat_names:[{stat_name:"1",stat_count:17},{stat_name:"2",stat_count:59}],StartDatetime:"2025-04-04 12:00:00",EndDatetime:"2025-04-04 12:30:00"},{stat_names:[{stat_name:"2",stat_count:73},{stat_name:"1",stat_count:25}],StartDatetime:"2025-04-04 12:30:00",EndDatetime:"2025-04-04 13:00:00"},{stat_names:[{stat_name:"2",stat_count:77},{stat_name:"1",stat_count:47}],StartDatetime:"2025-04-04 13:00:00",EndDatetime:"2025-04-04 13:30:00"},{stat_names:[{stat_name:"2",stat_count:11},{stat_name:"1",stat_count:41}],StartDatetime:"2025-04-04 13:30:00",EndDatetime:"2025-04-04 14:00:00"},{stat_names:[{stat_name:"1",stat_count:94},{stat_name:"2",stat_count:21}],StartDatetime:"2025-04-04 14:00:00",EndDatetime:"2025-04-04 14:30:00"},{stat_names:[{stat_name:"1",stat_count:9},{stat_name:"2",stat_count:24}],StartDatetime:"2025-04-04 14:30:00",EndDatetime:"2025-04-04 15:00:00"},{stat_names:[{stat_name:"2",stat_count:27},{stat_name:"1",stat_count:23}],StartDatetime:"2025-04-04 15:00:00",EndDatetime:"2025-04-04 15:30:00"},{stat_names:[{stat_name:"2",stat_count:9},{stat_name:"1",stat_count:6}],StartDatetime:"2025-04-04 15:30:00",EndDatetime:"2025-04-04 16:00:00"}]},{stat_group:"SCAN01",stat_columns:[{stat_name:"Critical",stat_name_disp:"Critical"},{stat_name:"NoData",stat_name_disp:"No Data"},{stat_name:"NoRead",stat_name_disp:"No Read"},{stat_name:"ReadConflict",stat_name_disp:"Read Conflict"},{stat_name:"Success",stat_name_disp:"Success"}],TimeRanges:[{stat_names:[{stat_name:"NoRead",stat_count:5},{stat_name:"Success",stat_count:181}],StartDatetime:"2025-04-04 11:30:00",EndDatetime:"2025-04-04 12:00:00"},{stat_names:[{stat_name:"Success",stat_count:83}],StartDatetime:"2025-04-04 12:00:00",EndDatetime:"2025-04-04 12:30:00"},{stat_names:[{stat_name:"Success",stat_count:135},{stat_name:"NoRead",stat_count:8}],StartDatetime:"2025-04-04 12:30:00",EndDatetime:"2025-04-04 13:00:00"},{stat_names:[{stat_name:"Success",stat_count:207},{stat_name:"NoRead",stat_count:8}],StartDatetime:"2025-04-04 13:00:00",EndDatetime:"2025-04-04 13:30:00"},{stat_names:[{stat_name:"Success",stat_count:88},{stat_name:"NoRead",stat_count:4}],StartDatetime:"2025-04-04 13:30:00",EndDatetime:"2025-04-04 14:00:00"},{stat_names:[{stat_name:"Success",stat_count:116},{stat_name:"NoRead",stat_count:1},{stat_name:"NoData",stat_count:1}],StartDatetime:"2025-04-04 14:00:00",EndDatetime:"2025-04-04 14:30:00"},{stat_names:[{stat_name:"Success",stat_count:76},{stat_name:"NoRead",stat_count:6}],StartDatetime:"2025-04-04 14:30:00",EndDatetime:"2025-04-04 15:00:00"},{stat_names:[{stat_name:"Success",stat_count:129},{stat_name:"NoRead",stat_count:11}],StartDatetime:"2025-04-04 15:00:00",EndDatetime:"2025-04-04 15:30:00"},{stat_names:[{stat_name:"Success",stat_count:49},{stat_name:"NoRead",stat_count:7}],StartDatetime:"2025-04-04 15:30:00",EndDatetime:"2025-04-04 16:00:00"}]}]}})
	const isLoading = ref(false)

	/*=====================================================================
    COMPUTED PROPERTIES 
  =====================================================================*/


	/*=====================================================================
			FUNCTIONS
	=====================================================================*/
	const formatDateBoxDisplay = (date) => {
		if (!date) return '';
		const d = new Date(date);
		const pad = (n) => n.toString().padStart(2, '0');

		const month = pad(d.getMonth() + 1);
		const day = pad(d.getDate());
		const year = d.getFullYear().toString().slice(-2);

		let hours = d.getHours();
		const minutes = pad(d.getMinutes());
		const ampm = hours >= 12 ? 'PM' : 'AM';
		hours = hours % 12 || 12;

		return `${month}/${day}/${year} ${pad(hours)}:${minutes} ${ampm}`;
	};
	const formatShortDateTime = (date) => {
		const d = new Date(date.replace(" ", "T"));
		let formattedDate = null;
		console.log('isMobile:', props.isMobile)
		if (props.isMobile){
			formattedDate = d.toLocaleString('en-US', {
					hour: '2-digit',
					minute: '2-digit',
					hour12: true
			});
		}else{
			formattedDate = d.toLocaleString('en-US', {
															year: '2-digit',    // Use two-digit year (e.g., '25' instead of '2025')
															month: '2-digit',   // Use two-digit month (e.g., '03')
															day: '2-digit',     // Use two-digit day (e.g., '22')
															hour: '2-digit',    // Use two-digit hour (e.g., '06' for 6 PM)
															minute: '2-digit',  // Use two-digit minute (e.g., '30')
															hour12: true        // Use 12-hour format with AM/PM
			}).replace(",", " ");   // Remove the comma separating date and time
		}
		return formattedDate;
	}
	
	const formatDateTime = (date) => {
		console.log('date:', date)
		const d = date || new Date();
		
		// Get year, month, day
		const year = d.getFullYear();
		const month = String(d.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed
		const day = String(d.getDate()).padStart(2, '0');
		
		// Get hours, minutes, seconds
		const hours = String(d.getHours()).padStart(2, '0');
		const minutes = String(d.getMinutes()).padStart(2, '0');
		const seconds = String(d.getSeconds()).padStart(2, '0');
		
		// Return the formatted string with a space between date and time
		return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
	}

	const getConnectionStatsData = async () => {
		try {
			nextTick(() => {
				isLoading.value = true
			})
			
			

			console.log('getConnectionStatsData:',new Date(endDateTime.value- (queryHours.value * 60 * 60 * 1000)))
			
			const start = endDateTime.value ? new Date(endDateTime.value - (queryHours.value * 60 * 60 * 1000)): new Date(Date.now() - (queryHours.value * 60 * 60 * 1000))

			const params = {
				Timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
				StartDatetime: formatDateTime(start),
				EndDatetime: formatDateTime(endDateTime.value),
				IntervalMins: timeInterval.value
			}

		
			const res = await api.get(`RemoteStats/OverallScreen/${props.siteID}/${props.connectionID}`, params);

			// DEMO
			// setTimeout(() => {
			// 	isLoading.value = false
			// }, 500);
			// const res = dummyData.value

			res.data.stat_groups.forEach(statGroup => {
				// Get the column names (stat properties) we need to initialize
				const statNames = statGroup.stat_columns.map(col => col.stat_name);
				
				// Process each time range within this group
				statGroup.TimeRanges.forEach(timeRange => {
					// Initialize all stats to 0
					statNames.forEach(statName => { timeRange[statName] = 0; });
					
					// Set actual values where they exist
					timeRange.stat_names.forEach(stat => { timeRange[stat.stat_name] = stat.stat_count; });
				});
			});

			gridData.value = res.data

			console.log('good response:', res)
		}catch (error) { 
			
			gridData.value.stat_groups = []
			gridMsgError.value = error.response.data.message
			console.log('ERROR:', error.response.data.message)
		}
		finally {
			isLoading.value = false
		}
	}

	const onRefreshClick = () => {
		endDateTime.value = endDateTime.value ? endDateTime.value: new Date();
		console.log('onRefreshClick:', endDateTime.value)
		getConnectionStatsData()
	}

	/*=====================================================================
    WATCHERS 
  =====================================================================*/
	watch(() => props.connectionID, (newData, oldData) => { 

		console.log('Watcher NEW:', newData)
		console.log('Watcher OLD:', oldData)
		gridMsgError.value = ''
		endDateTime.value = new Date();

		if (newData) {
			getConnectionStatsData()
		}


	}, { immediate: true });


</script>

<style scoped lang="scss">
	@import "../themes/generated/variables.additional.scss";

	.popup-content {
		display: flex;
		flex-direction: column;
		flex: 1 1 auto;
		color: rgba($base-text-color, 0.75)!important;
		padding: 20px;
		gap: 30px;
		height: 100%;
	}

	.row {
		display: flex;
		gap: 20px;
		flex-shrink: 0; 
		align-items: end;
		color: rgba($base-text-color, 0.75)!important;
	}

	.accent {
		color: $base-accent;
	}
	.bg-accent {
		background-color: $base-accent;
	}

	/* DX Controls Override  */
	::v-deep(.dx-texteditor) {
		border: 0px;
		border-radius: 5px;
		background-color: rgba($base-bg-dark, .65);
		padding: 0 10px;

	}
	::v-deep(.dx-texteditor-input) {
		color: rgba($base-text-color, 0.75)!important;
	}		
	.dx-texteditor.dx-editor-filled.dx-state-disabled {
		background-color: rgba($base-bg-dark, .65);
		opacity: 1;
	}
	.dx-texteditor::before {
		content: none;
	}
	.dx-texteditor::after {
		content: none;
	}

	.custom-button.dx-button, .dx-button.dx-button-default , .dx-item-content.dx-button.dx-button-default {
		border-radius: 5px;
		box-shadow: none;
		height: 31px;
		margin-bottom: 1px;
		color: $base-text-color!important; //rgba($base-text-color, alpha($base-text-color)  * 0.85);
	}

	.custom-button {
		border-radius: 5px;
		box-shadow: none;
		height: 31px;
		margin-bottom: 1px;
		color: $base-text-color!important; //rgba($base-text-color, alpha($base-text-color)  * 0.85);
	}

	.custom-button.dx-button-has-icon ,.dx-button-content {
		min-width: unset!important;
		height: unset!important;
	}

	::v-deep(.dx-button-has-icon ){
		width: unset!important;
		height: unset!important;
	}

	::v-deep(.custom-button.dx-button-has-icon .dx-icon) {
		font-size: 10px !important;
		width: unset;
		height: unset;
	}

	::v-deep(.dx-datagrid ){
		color: rgba($base-text-color, 0.75)!important;
	}

	::v-deep(.dx-header-row){
		background-color: rgba($base-bg-dark, .65);
	}

	::v-deep(.dx-empty-message) {
		margin-top: 40px;
		font-size: 16px;
		color: rgba($base-text-color, 0.75)!important;

	}

	::v-deep(.dx-tab) {
		// border: 1px solid $base-border-color;
		border-bottom: 0;
		padding: 10px 25px;
		border-radius: 10px 10px 0px 0px;

	}
	::v-deep(.dx-tabs) {
		width: 200px;
		border-radius: 10px 10px 0px 0px;
	}

	::v-deep(.dx-tab.dx-tab-selected) {
		background-color: rgba($base-bg-dark, .65);
		// border: 1px solid $base-border-color;
		border-bottom: 0;

		border-radius: 10px 10px 0px 0px;
	}
	::v-deep(.dx-tab.dx-state-focused) {
		background-color: rgba($base-bg-dark, .65)!important;
	}
	::v-deep(.dx-tab.dx-state-hover) {
		background-color: rgba($base-bg-dark, .65)!important;
	}
	::v-deep(.dx-tab.dx-state-active) {
		background-color: rgba($base-bg-dark, .65)!important;
	}

	::v-deep(.dx-tabs-wrapper ){
		position: relative;
		display: flex;
		border: 1px solid $base-border-color;
		// border-right: 2px solid $base-border-color;
		border-bottom: 0;
		border-radius: 10px 10px 0px 0px;
	

	}

	::v-deep(.dx-tabpanel-container .dx-multiview-wrapper) {
		padding: 20px;
		background-color: #2f2e38;
    border: 1px solid #515159;
		border-radius: 0px 10px 10px 10px;
	}
	
	::v-deep(.dxc-title text) {
		font-size: 16px!important;
	}
</style>