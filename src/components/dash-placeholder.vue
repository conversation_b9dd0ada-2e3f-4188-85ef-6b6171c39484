<template>

	<div class="centered-message">
      {{ message }}
    </div>

  <div class="dashboard-container">

    <!-- Centered Message -->
    

    <!-- Dashboard Grid -->
    <div class="dashboard-grid">
      <!-- KPI Cards -->
      <div class="kpi-section">
        <div class="kpi-card">
          <div class="skeleton-kpi">
            <div class="skeleton-kpi-title"></div>
            <div class="skeleton-kpi-value"></div>
            <div class="skeleton-kpi-change"></div>
          </div>
        </div>
        
        <div class="kpi-card">
          <div class="skeleton-kpi">
            <div class="skeleton-kpi-title"></div>
            <div class="skeleton-kpi-value"></div>
            <div class="skeleton-kpi-change"></div>
          </div>
        </div>
        
        <div class="kpi-card">
          <div class="skeleton-kpi">
            <div class="skeleton-kpi-title"></div>
            <div class="skeleton-kpi-value"></div>
            <div class="skeleton-kpi-change"></div>
          </div>
        </div>
      </div>

      <!-- Line Chart -->
      <div class="chart-container">
        <div class="chart-content">
          <div class="skeleton-line-chart">
            <div class="chart-axes">
              <div class="y-axis">
                <div v-for="n in 5" :key="n" class="y-axis-label"></div>
              </div>
              <div class="x-axis">
                <div v-for="n in 7" :key="n" class="x-axis-label"></div>
              </div>
            </div>
            <svg class="chart-svg" viewBox="0 0 400 200">
              <defs>
                <linearGradient id="shimmer" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" style="stop-color:#ACACAF;stop-opacity:1" />
                  <stop offset="50%" style="stop-color:#c0c0c0;stop-opacity:1" />
                  <stop offset="100%" style="stop-color:#ACACAF;stop-opacity:1" />
                  <animateTransform attributeName="gradientTransform" type="translate" 
                    values="-100 0; 400 0; -100 0" dur="2s" repeatCount="indefinite"/>
                </linearGradient>
              </defs>
              <polyline points="50,150 100,120 150,100 200,80 250,90 300,60 350,40" 
                fill="none" stroke="url(#shimmer)" stroke-width="3"/>
              <circle v-for="(point, index) in [50,100,150,200,250,300,350]" 
                :key="index" :cx="point" :cy="150-index*15" r="4" fill="url(#shimmer)"/>
            </svg>
          </div>
         
        </div>
      </div>

      <!-- Bar Chart -->
      <div class="chart-container">
        <div class="chart-content">
          <div class="skeleton-bar-chart">
            <svg class="chart-svg" viewBox="0 0 400 200">
              <defs>
                <linearGradient id="barShimmer" x1="0%" y1="0%" x2="0%" y2="100%">
                  <stop offset="0%" style="stop-color:#ACACAF;stop-opacity:1" />
                  <stop offset="50%" style="stop-color:#c0c0c0;stop-opacity:1" />
                  <stop offset="100%" style="stop-color:#ACACAF;stop-opacity:1" />
                  <animateTransform attributeName="gradientTransform" type="translate" 
                    values="0 -200; 0 200; 0 -200" dur="2s" repeatCount="indefinite"/>
                </linearGradient>
              </defs>
              <rect v-for="(bar, index) in [60,80,45,90,70]" :key="index" 
                :x="60 + index * 60" :y="200 - bar" width="40" :height="bar" 
                fill="url(#barShimmer)" rx="4"/>
            </svg>
          </div>
          
        </div>
      </div>

      <!-- Donut Chart -->
      <div class="chart-container">
        <div class="chart-content">
          <div class="skeleton-donut-chart">
            <svg class="chart-svg" viewBox="0 0 200 200">
              <defs>
                <linearGradient id="donutShimmer" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style="stop-color:#ACACAF;stop-opacity:1" />
                  <stop offset="50%" style="stop-color:#c0c0c0;stop-opacity:1" />
                  <stop offset="100%" style="stop-color:#ACACAF;stop-opacity:1" />
                  <animateTransform attributeName="gradientTransform" type="rotate" 
                    values="0 100 100; 360 100 100" dur="3s" repeatCount="indefinite"/>
                </linearGradient>
              </defs>
              <circle cx="100" cy="100" r="60" fill="none" stroke="url(#donutShimmer)" stroke-width="30"/>
              <circle cx="100" cy="100" r="25" fill="#2F2E38"/>
            </svg>
            <div class="donut-legend">
              <div v-for="n in 4" :key="n" class="legend-item">
                <div class="legend-color skeleton-shimmer"></div>
                <div class="legend-label skeleton-shimmer"></div>
              </div>
            </div>
          </div>
          
        </div>
      </div>

			<!-- Table Grid -->
      <div class="chart-container ">
        <div class="chart-content">
          <div class="skeleton-table-grid">
            <div class="table-header">
              <div class="table-cell header-cell">
              </div>
              <div class="table-cell header-cell">
              </div>
              <div class="table-cell header-cell">
              </div>
            </div>
            
            <div class="table-body">
              <div v-for="row in 8" :key="row" class="table-row">
                <div class="table-cell">
                  <div class="skeleton-table-text skeleton-shimmer" :style="{width: Math.random() * 40 + 60 + '%'}"></div>
                </div>
                <div class="table-cell">
                  <div class="skeleton-table-number skeleton-shimmer" :style="{width: Math.random() * 30 + 50 + '%'}"></div>
                </div>
                <div class="table-cell">
                  <div class="skeleton-table-number skeleton-shimmer" :style="{width: Math.random() * 25 + 45 + '%'}"></div>
                </div>
                
              </div>
            </div>
          </div>
        </div>
      </div>

			<!-- Bar Chart -->
      <div class="chart-container chart-span-2">
        <div class="chart-content">
          <div class="skeleton-bar-chart">
            <svg class="chart-svg" viewBox="0 0 900 200" preserveAspectRatio="none">
              <defs>
                <linearGradient id="barShimmer" x1="0%" y1="0%" x2="0%" y2="100%">
                  <stop offset="0%" style="stop-color:#ACACAF;stop-opacity:1" />
                  <stop offset="50%" style="stop-color:#c0c0c0;stop-opacity:1" />
                  <stop offset="100%" style="stop-color:#ACACAF;stop-opacity:1" />
                  <animateTransform attributeName="gradientTransform" type="translate" 
                    values="0 -200; 0 200; 0 -200" dur="2s" repeatCount="indefinite"/>
                </linearGradient>
              </defs>
              <rect v-for="(bar, index) in [60,80,45,90,70,25,55,89,102,60,80,45,90]" :key="index" 
                :x="60 + index * 60" :y="200 - bar" width="40" :height="bar" 
                fill="url(#barShimmer)" rx="4"/>
            </svg>
          </div>
          
        </div>
      </div>

    </div>
  </div>
</template>

<script setup>
import { ref, defineProps } from 'vue';

const props = defineProps({
  message: {
    type: String,
    required: false,
    default: 'Select Site To Render Dashboard'
  }
});

</script>

<style scoped>
.dashboard-container {
  position: relative;
  min-height: 100%;
	overflow: hidden;
	opacity: .4;
}

.centered-message {
  position:absolute;
  top: 250px;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1rem;
  font-weight: 500;
  color: #3aafa9;
  background: rgba(47, 46, 56, 0.91);
  padding: 20px 40px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(172, 172, 175, 0.2);
  z-index: 1000;
  text-align: center;
  max-width: 90vw;
  word-wrap: break-word;

    .screen-x-small & {
      width: 100%;
      
    } 
}

.dashboard-title {
  font-size: 2rem;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 20px;
  
}

.controls-section {
  display: flex;
  gap: 20px;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: #2F2E38;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.3);
}

.dropdown-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.dropdown-label {
  font-weight: 500;
  color: #ACACAF;
}

.dropdown {
  padding: 8px 12px;
  border: 1px solid #ACACAF;
  border-radius: 6px;
  font-size: 14px;
  min-width: 200px;
  background-color: #2F2E38;
  color: #ACACAF;
}

.toggle-btn {
  padding: 8px 16px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.toggle-btn:hover {
  background-color: #2563eb;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.kpi-section {
  grid-column: 1 / -1;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.kpi-card {
  background: #2F2E38;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.3);
}

.skeleton-kpi {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.skeleton-kpi-title {
  height: 16px;
  background: linear-gradient(90deg, #ACACAF 25%, #c0c0c0 50%, #ACACAF 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
  border-radius: 4px;
  width: 60%;
}

.skeleton-kpi-value {
  height: 32px;
  background: linear-gradient(90deg, #ACACAF 25%, #c0c0c0 50%, #ACACAF 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
  border-radius: 4px;
  width: 80%;
}

.skeleton-kpi-change {
  height: 14px;
  background: linear-gradient(90deg, #ACACAF 25%, #c0c0c0 50%, #ACACAF 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
  border-radius: 4px;
  width: 40%;
}

.kpi-content h3 {
  margin: 0 0 8px 0;
  color: #ACACAF;
  font-size: 14px;
  font-weight: 500;
}

.kpi-value {
  font-size: 2rem;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 4px;
}

.kpi-change {
  font-size: 14px;
  font-weight: 500;
}

.kpi-change.positive {
  color: #10b981;
}

.kpi-change.negative {
  color: #ef4444;
}

.chart-container {
  background: #2F2E38;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.3);
  overflow: hidden;
}

.chart-header {
  padding: 20px 20px 10px 20px;
  border-bottom: 1px solid #ACACAF;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-header h3 {
  margin: 0;
  color: #ffffff;
  font-size: 1.1rem;
}

.loading-indicator {
  font-size: 12px;
  color: #ACACAF;
  font-style: italic;
}

.chart-content {
  padding: 20px;
  height: 250px;
}

.chart-svg {
  width: 100%;
  height: 100%;
}

.skeleton-line-chart {
  position: relative;
  height: 100%;
}

.chart-axes {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
}

.y-axis {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding-right: 10px;
}

.y-axis-label {
  height: 12px;
  width: 30px;
  background: linear-gradient(90deg, #ACACAF 25%, #c0c0c0 50%, #ACACAF 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
  border-radius: 2px;
}

.x-axis {
  position: absolute;
  bottom: 0;
  left: 50px;
  right: 0;
  display: flex;
  justify-content: space-between;
}

.x-axis-label {
  height: 12px;
  width: 40px;
  background: linear-gradient(90deg, #ACACAF 25%, #c0c0c0 50%, #ACACAF 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
  border-radius: 2px;
}

.donut-legend {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
}

.legend-label {
  height: 12px;
  width: 80px;
  border-radius: 2px;
}

.skeleton-shimmer {
  background: linear-gradient(90deg, #ACACAF 25%, #c0c0c0 50%, #ACACAF 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.skeleton-donut-chart {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Table Grid Skeleton Styles */
.skeleton-table-grid {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr ;
  gap: 1px;
  background-color: #1a1a1a;
  border-radius: 6px 6px 0 0;
  overflow: hidden;
}

.table-body {
  display: flex;
  flex-direction: column;
  gap: 1px;
  background-color: #1a1a1a;
  border-radius: 0 0 6px 6px;
  overflow: hidden;
  flex: 1;
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr ;
  gap: 1px;
}

.table-cell {
  background-color: #2F2E38;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  min-height: 20px;
}

.header-cell {
  background-color: #26252E;
  font-weight: 600;
  border-bottom: 2px solid #1a1a1a;
}

.skeleton-table-text {
  height: 14px;
  border-radius: 3px;
  min-width: 60px;
}

.skeleton-table-number {
  height: 14px;
  border-radius: 3px;
  min-width: 40px;
}

.skeleton-table-badge {
  height: 20px;
  border-radius: 10px;
  min-width: 30px;
}

.skeleton-table-icon {
  width: 16px;
  height: 16px;
  border-radius: 2px;
}



/* Chart spanning utilities */
.chart-span-2 {
  grid-column: span 2;
}

.chart-span-3 {
  grid-column: span 3;
}

.chart-span-full {
  grid-column: 1 / -1;
}

/* Responsive adjustments for spans */
@media (max-width: 768px) {
  .chart-span-2,
  .chart-span-3 {
    grid-column: span 1;
  }
}
</style>